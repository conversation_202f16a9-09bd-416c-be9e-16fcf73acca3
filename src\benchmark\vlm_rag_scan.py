"""
Module providing an object-oriented interface to evaluate a Vision RAG system from Live Intelligence.

All configuration (API keys, model names, paths) is handled via instance attributes or environment variables.
"""

# High level dependencies
import dspy
import os
import logging
from typing import List, Dict, Any, Tuple, Optional
import time
import sys
from datasets import load_dataset

# Add the project root to the Python path
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parents[2]))
import glob
import json
import zlib
from tqdm.asyncio import tqdm as async_tqdm
from io import BytesIO
from PIL import Image
import pandas as pd
from src.api.live_api import FileUploader, Files, AsyncDocumentSearch


class VisionRAGScan:
    """
    Class to evaluate a Vision RAG system from Live Intelligence.

    This class handles downloading the dataset, extracting and uploading images,
    preparing question/answer pairs, running asynchronous API requests,
    and evaluating answers using a DSPy judge. Results are exported to a CSV file.
    All configuration (API keys, models, paths) is provided via parameters or environment variables.
    """
    def __init__(
        self,
        n_questions: int,
        tool: str,
        workspace_id: int,
        api_key: str,
        base_url: str,
        model: str = "alfred-4.1",
        images_dir: str = "images",
        log_level: int = logging.INFO,
    ) -> None:
        """
        Initialize the VisionRAGScan class with the required parameters.

        Args:
            n_questions (int): Number of questions to process.
            tool (str): Tool to use for evaluation.
            workspace_id (int): Target workspace ID.
            api_key (str): API key for service access.
            base_url (str): Base URL of the API.
            model (str, optional): Model name to use. Default is 'alfred-4.1'.
            images_dir (str, optional): Directory to store images. Default is 'images'.
            log_level (int, optional): Logging level. Default is logging.INFO.
        """
        self.n_questions = n_questions
        self.tool = tool
        self.workspace_id = workspace_id
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.images_dir = images_dir
        self.logger = logging.getLogger("VLMVisionRAGScan")
        self.logger.setLevel(log_level)
        os.makedirs(self.images_dir, exist_ok=True)
        Image.MAX_IMAGE_PIXELS = None

    def download_dataset_and_images(self) -> None:
        """
        Download the InfographicsVQA dataset, extract images and question/answer pairs,
        and save them locally in the images_dir folder. The questions/answers are
        saved in a JSON file.
        Note : Performs a deduplication of images based on their CRC because the dataset may contain multiple questions for the same image.
        """
        self.logger.info("Loading dataset and extracting images...")
        ds = load_dataset("Ryoo72/InfographicsVQA")
        all_samples = ds["test"].select(range(self.n_questions))
        image_crcs: Dict[int, int] = {}
        idx_to_imagefile: Dict[int, Dict[str, Any]] = {}
        for idx, sample in enumerate(all_samples):
            img = sample["image"]
            img_byte_arr = BytesIO()
            img.save(img_byte_arr, format=img.format)
            img_byte_arr = img_byte_arr.getvalue()
            crc = zlib.crc32(img_byte_arr)
            if crc in image_crcs:
                idx_to_imagefile[idx] = {
                    "image_file": f"image_{image_crcs[crc]}.jpg",
                    "crc": crc,
                }
                continue
            image_crcs[crc] = idx
            img.save(f"{self.images_dir}/image_{idx}.jpg")
            idx_to_imagefile[idx] = {"image_file": f"image_{idx}.jpg", "crc": crc}
        self.logger.info(f"{len(image_crcs)} unique images downloaded.")
        # Prepare questions/answers
        qa_data_list: List[Dict[str, Any]] = []
        for idx, sample in enumerate(all_samples):
            questions = sample["question"]
            answers = sample["answers"]
            image_info = idx_to_imagefile[idx]
            if isinstance(questions, list):
                for q, a in zip(questions, answers):
                    qa_data_list.append(
                        {
                            "image_file": image_info["image_file"],
                            "crc": image_info["crc"],
                            "question": q,
                            "answers": a,
                        }
                    )
            else:
                qa_data_list.append(
                    {
                        "image_file": image_info["image_file"],
                        "crc": image_info["crc"],
                        "question": questions,
                        "answers": answers,
                    }
                )
        with open(f"{self.images_dir}/qa_data.json", "w", encoding="utf-8") as f:
            json.dump(qa_data_list, f, indent=2, ensure_ascii=False)
        self.logger.info(f"Questions/answers saved to {self.images_dir}/qa_data.json")

    def upload_images(self) -> Dict[str, Any]:
        """
        Upload all images from the images_dir folder to the target workspace via the API.
        Returns a summary of successful/failed uploads.
        """
        self.logger.info("Uploading images to the target workspace...")
        uploader = FileUploader(api_key=self.api_key, base_url=self.base_url)
        image_files: List[str] = [
            f
            for f in glob.glob(f"{self.images_dir}/*")
            if f.lower().endswith((".jpg", ".jpeg", ".png"))
        ]
        result = uploader.upload_files_in_batches(
            file_paths=image_files,
            batch_size=10,
            max_wait_time=3600,
            check_interval=30,
            workspace_id=self.workspace_id,
        )
        self.logger.info(
            f"Upload finished: {result['successful_uploads']} successful, {result['failed_uploads']} failed."
        )
        return result

    def get_uploaded_files_mapping(self) -> Dict[str, Any]:
        """
        Retrieve the mapping between local image filenames and their API IDs
        after upload to the workspace.
        """
        self.logger.info("Retrieving local image <-> API ID mapping...")
        files_api = Files(api_key=self.api_key, base_url=self.base_url)
        uploaded_files = files_api.execute(workspace_scope=self.workspace_id)
        filename_to_id: Dict[str, Any] = {}
        for f in uploaded_files:
            if f["filename"].startswith("image_") and f["filename"].endswith(".pdf"):
                local_name = f["filename"].replace(".pdf", ".jpg")
                filename_to_id[local_name] = f["id"]
        return filename_to_id

    def prepare_questions(self) -> Tuple[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]:
        """
        Load and organize questions/answers from the JSON file generated during dataset download.
        Returns the complete list and a mapping image -> questions.
        """
        with open(f"{self.images_dir}/qa_data.json", "r", encoding="utf-8") as f:
            qa_data: List[Dict[str, Any]] = json.load(f)
        image_to_questions: Dict[str, List[Dict[str, Any]]] = {}
        for item in qa_data:
            img_file = item["image_file"]
            if img_file not in image_to_questions:
                image_to_questions[img_file] = []
            image_to_questions[img_file].append(
                {"question": item["question"], "answers": item["answers"]}
            )
        return qa_data, image_to_questions

    def prepare_async_requests(self, image_to_questions: Dict[str, List[Dict[str, Any]]]) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Prepare asynchronous API requests and the list of target image names for evaluation.
        Args:
            image_to_questions (dict): Mapping image -> questions/answers.
        Returns:
            tuple: (list of requests, list of target image names)
        """
        async_requests: List[Dict[str, Any]] = []
        target_image_names: List[str] = []
        for img_file, qas in image_to_questions.items():
            for qa in qas:
                req = {
                    "query": qa["question"],
                    "workspace_ids": [self.workspace_id],
                    "tool": self.tool,
                    "model": self.model,
                }
                async_requests.append(req)
                target_image_names.append(
                    img_file.replace(".jpg", "")
                    .replace(".jpeg", "")
                    .replace(".png", "")
                )
        return async_requests, target_image_names

    def prepare_judge(self) -> Any:
        """
        Prepare the DSPy judge for automatic evaluation of model answers.
        Returns an instance of the judge.
        """
        self.logger.info("Preparing DSPy judge...")

        class JudgeSignature(dspy.Signature):
            question = dspy.InputField(desc="The question asked to the VLM")
            vlm_answer = dspy.InputField(desc="The answer provided by the VLM")
            expected_answers = dspy.InputField(
                desc="List of acceptable answers from the dataset"
            )
            judgment = dspy.OutputField(
                desc="'YES' if the answer is correct, 'NO' otherwise"
            )

        class VLMJudge(dspy.Module):
            def __init__(self) -> None:
                super().__init__()
                self.judge = dspy.ChainOfThought(JudgeSignature)
                self.logger = logging.getLogger("VLMJudge")

            def forward(
                self, question: str, vlm_answer: str, expected_answers: List[str]
            ) -> str:
                try:
                    expected_str = ", ".join(expected_answers)
                    response = self.judge(
                        question=question,
                        vlm_answer=vlm_answer,
                        expected_answers=expected_str,
                    )
                    judgment = response.judgment.strip().upper()
                    if judgment == "YES" or judgment == "NO":
                        return judgment
                    else:
                        self.logger.error(
                            f"Invalid judge response: {judgment}. Defaulting to NO."
                        )
                        return "NO"
                except Exception as e:
                    self.logger.error(f"Judge error: {str(e)}")
                    return "NO"

        return VLMJudge()

    async def run_scan(self, csv_output_path: str) -> None:
        """
        Run the asynchronous evaluation of the VLM model on the prepared questions,
        evaluate the answers with the DSPy judge, and export the results to a CSV file.

        Args:
            csv_output_path (str): Path to the output CSV file.
        """
        self.logger.info("Starting VLM async scan...")
        qa_data, image_to_questions = self.prepare_questions()
        async_requests, target_image_names = self.prepare_async_requests(
            image_to_questions
        )
        async_api = AsyncDocumentSearch(api_key=self.api_key, base_url=self.base_url)
        results: List[Dict[str, Any]] = []
        all_questions: List[str] = []
        all_vlm_answers: List[str] = []
        all_expected_answers: List[List[str]] = []
        found_image_names_list: List[List[str]] = []

        def progress_callback(done: int, total: int) -> None:
            if done % 10 == 0 or done == total:
                self.logger.info(f"{done}/{total} requests processed...")

        start_time = time.time()
        batch_results = await async_api.execute_batch(
            requests_data=async_requests,
            max_concurrent=10,
            progress_callback=progress_callback,
        )
        total_time = time.time() - start_time
        for i, (req, res) in enumerate(zip(async_requests, batch_results)):
            question = req["query"]
            answers = qa_data[i]["answers"] if i < len(qa_data) else []
            api_response = (
                res["result"] if res["error"] is None else {"error": res["error"]}
            )
            vlm_answer = ""
            found_image_names: List[str] = []
            if "error" not in api_response:
                vlm_answer = (
                    api_response.get("answer", "")
                    or api_response.get("content", "")
                    or str(api_response)
                )
                if "documents" in api_response:
                    for doc in api_response.get("documents", []):
                        name = doc.get("name") or doc.get("filename", "")
                        found_image_names.append(name)
            else:
                vlm_answer = "ERROR: Unable to process question"
            found_image_names_list.append(found_image_names)
            results.append(
                {
                    "question": question,
                    "expected_answers": answers,
                    "answer": vlm_answer,
                    "expected_images": target_image_names[i],
                    "retrieved_images": found_image_names,
                    "chunks_retrieved": (
                        api_response.get("documents", [])
                        if "documents" in api_response
                        else []
                    ),
                    "api_response": api_response,
                }
            )
            all_questions.append(question)
            all_vlm_answers.append(vlm_answer)
            all_expected_answers.append(
                answers if isinstance(answers, list) else [answers]
            )
        # DSPy judge
        judge = self.prepare_judge()
        self.logger.info("Evaluating answers with DSPy judge...")
        judgments: List[str] = []
        for q, a, exp in zip(all_questions, all_vlm_answers, all_expected_answers):
            try:
                verdict = judge(q, a, exp)
            except Exception as e:
                self.logger.error(f"Error during judgment: {e}")
                verdict = "ERROR"
            judgments.append(verdict)
        for i, result in enumerate(results):
            result["judgement"] = judgments[i] if i < len(judgments) else "NO_JUDGMENT"
        correct_answers = judgments.count("YES")
        total_judged = len(judgments)
        accuracy = (correct_answers / total_judged) * 100 if total_judged > 0 else 0
        correct_image_found = 0
        for i, found_names in enumerate(found_image_names_list):
            target_name = target_image_names[i]
            if target_name in found_names:
                correct_image_found += 1
        recall = (
            (correct_image_found / len(found_image_names_list)) * 100
            if len(found_image_names_list) > 0
            else 0
        )
        # Export CSV
        df = pd.DataFrame(
            [{k: v for k, v in r.items() if k != "api_response"} for r in results]
        )
        # Add summary row
        summary = {
            "question": "[REVIEW]",
            "expected_answers": "",
            "answer": "",
            "expected_images": "",
            "retrieved_images": "",
            "chunks_retrieved": "",
            "judgement": f"Total time: {total_time:.2f}s, Total: {len(results)}, Accuracy: {accuracy:.2f}%, Recall: {recall:.2f}%",
        }
        df = pd.concat([df, pd.DataFrame([summary])], ignore_index=True)
        df.to_csv(csv_output_path, index=False, encoding="utf-8")
        self.logger.info(f"Results exported to {csv_output_path}")
