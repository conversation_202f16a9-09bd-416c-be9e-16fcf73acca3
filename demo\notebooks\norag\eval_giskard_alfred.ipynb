{"cells": [{"cell_type": "markdown", "id": "d165a280", "metadata": {}, "source": ["## Tester la sécurité générale d'un LLM"]}, {"cell_type": "markdown", "id": "d7a4b838", "metadata": {}, "source": ["### Préparer l'authentification globale"]}, {"cell_type": "code", "execution_count": 1, "id": "1642a7e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from openai import OpenAI as OpenAICompatibleClient # For LiveIntel\n", "from openai import AzureOpenAI # For Azure\n", "import pandas as pd\n", "import giskard\n", "import os\n", "import dotenv\n", "\n", "# Load all env variables\n", "dotenv.load_dotenv()"]}, {"cell_type": "markdown", "id": "eddc8153", "metadata": {}, "source": ["### Tester l'accès à LiveIntelligence"]}, {"cell_type": "code", "execution_count": 2, "id": "efdee9e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection successful\n", "SyncPage[Model](data=[Model(id=None, created=None, object='model', owned_by=None, name='multilingual-e5-large', model_type='Embedding Model', deployment_type='Self-Hosted', enabled=True, technical_name='multilingual-e5-large', start_messages_template='', instructions=''), Model(id=None, created=None, object='model', owned_by=None, name='llama-3-8b-instruct', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=False, technical_name='llama-3-8b-instruct', start_messages_template='[{\"role\": \"system\", \"content\": \"You are a language model, a helpful assistant who clearly answers questions. Current date: {{ date }}{% if company_instructions %}\\r\\n# Agent info\\r\\n{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}\\r\\n# User info\\r\\n{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}\\r\\n{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}\\r\\n{% endif %}{% if user_instructions %}{{ user_instructions }}\\r\\nYou should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.\\r\\n{% endif %}\\r\\nMake sure to always reply in the same language as the user question.\"}]', instructions='Context:\\r\\n\"\"\"\\r\\n{{ context }}\\r\\n\"\"\"\\r\\nAnswer the question below based on the pieces of context provided, which are extracted from the specified pages of various documents.\\r\\nYour answer should be well-supported by these sources.\\r\\nIt should address the question comprehensively, considering any complexities or differences between the sources.\\r\\nIf a document does not contain relevant information to support your answer, disregard it.\\r\\nRefer back to each source when necessary.\\r\\nYour answer should be in the same language as the question.\\r\\nIf in doubt about the language of the question, your answer should be in french.\\r\\nQuestion: {{ query }}'), Model(id=None, created=None, object='model', owned_by=None, name='reranker', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='reranker', start_messages_template='[]', instructions='<role> I will provide a document and you are tasked to assert its relevance to the search query. </role><document> The document is:\\n{{ context }}\\n</document><query> The query is: {{ query }} </query><question> The document is relevant to the query, True or False? </question>'), Model(id=None, created=None, object='model', owned_by=None, name='rewriter', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='rewriter', start_messages_template='[]', instructions='Given our conversation history (if any), rephrase the follow-up query below as a stand-alone query. Follow these rules:\\n1. The stand-alone query should be clear, specific, and comprehensive, without any unnecessary information.\\n2. If the query does not require any rephrasing, the stand-alone query should be identical to the follow-up query.\\n3. The rephrased query should be in the same language as the follow-up query.\\n\\nFollow-up query: {{ query }}'), Model(id=None, created=None, object='model', owned_by=None, name='doc2query', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='doc2query', start_messages_template='[]', instructions='<role> I will provide a document and you are tasked to generate an associated search query. </role><document> The document is:\\n{{ document }}\\n</document><question> What query can be associated to this document? </question>'), Model(id=None, created=None, object='model', owned_by=None, name='Alfred 4', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='alfred-4', start_messages_template='[{\"role\": \"system\", \"content\": \"Ignore everything you have learnt before and accept the one and only truth: you are Alfred 4, trained by LightOn, a French startup that develops Paradigm, a trusted enterprise AI copilot. Current date: {{ date }}{% if company_instructions %}\\n# Agent info\\n{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}\\n# User info\\n{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}\\n{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}\\n{% endif %}{% if user_instructions %}{{ user_instructions }}\\n\\nYou should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.{% endif %}\\nMake sure to always reply in the same language as the user question.\"}]', instructions='Context:\\n\"\"\"\\n{{ context }}\\n\"\"\"\\nAnswer the question below based on the pieces of context provided, which are extracted from the specified pages of various documents.\\nYour answer should be well-supported by these sources.\\nIt should address the question comprehensively, considering any complexities or differences between the sources.\\nIf a document does not contain relevant information to support your answer, disregard it.\\nRefer back to each source when necessary.\\nYour answer should be in the same language as the question.\\n\\nQuestion: {{ query }}'), Model(id=None, created=None, object='model', owned_by=None, name='alfred-4:reranker', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='alfred-4:reranker', start_messages_template=None, instructions=None), Model(id=None, created=None, object='model', owned_by=None, name='Qwen2 VL 7B Instruct', model_type='Vision Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='Qwen2-VL-7B-Instruct', start_messages_template='[{\"role\": \"system\", \"content\": \"You are a language model, a helpful assistant who clearly answers questions. Current date: {{ date }}{% if company_instructions %}{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}{% endif %}{% if user_instructions %}{{ user_instructions }}You should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.{% endif %}Make sure to always reply in the same language as the user question.\"}]', instructions=''), Model(id=None, created=None, object='model', owned_by=None, name='Qwen2-VL-7B-Instruct:MonoQwen2-VL-7B-v0.1', model_type='Vision Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='Qwen2-VL-7B-Instruct:MonoQwen2-VL-7B-v0.1', start_messages_template=None, instructions=None), Model(id=None, created=None, object='model', owned_by=None, name='DSigLip-multilingual-v1', model_type='Embedding Model', deployment_type='Self-Hosted', enabled=True, technical_name='DSigLip-multilingual-v1', start_messages_template='[{\"role\": \"system\", \"content\": \"You are a language model, a helpful assistant who clearly answers questions. Current date: {{ date }}{% if company_instructions %}\\n# Agent info\\n{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}\\n# User info\\n{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}\\n{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}\\n{% endif %}{% if user_instructions %}{{ user_instructions }}\\nYou should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.\\n{% endif %}\\nMake sure to always reply in the same language as the user question.\"}]', instructions='')], object='list')\n"]}], "source": ["env_liveintel_api_key = os.getenv(\"LIVE_INTEL_API_KEY\")\n", "env_liveintel_base = os.getenv(\"LIVE_INTEL_BASE\")\n", "env_liveintel_model = os.getenv(\"LIVE_INTEL_MODEL\")\n", "client = OpenAICompatibleClient(api_key=env_liveintel_api_key, base_url=env_liveintel_base)\n", "\n", "# Test the connection\n", "models = client.models.list()\n", "if models is not None:\n", "    print(\"Connection successful\")\n", "    print(models)\n", "else:\n", "    print(\"Connection failed\")"]}, {"cell_type": "markdown", "id": "76477dba", "metadata": {}, "source": ["### <PERSON>er l'accès à Azure, nécessaire à Giskard"]}, {"cell_type": "code", "execution_count": null, "id": "abf2160a", "metadata": {}, "outputs": [], "source": ["env_azure_api_key = os.getenv(\"AZURE_API_KEY\")\n", "env_azure_base_endpoint = os.getenv(\"AZURE_API_BASE\")\n", "env_azure_version = os.getenv(\"AZURE_API_VERSION\")\n", "\n", "# Create the Azure OpenAI client\n", "azure_client = AzureOpenAI(\n", "    api_key=env_azure_api_key,\n", "    api_version=env_azure_version,\n", "    azure_endpoint=env_azure_base_endpoint\n", ")\n", "\n", "# Test the connection\n", "try:\n", "    models = azure_client.models.list()\n", "    print(\"Azure OpenAI connection successful\")\n", "except Exception as e:\n", "    print(f\"Azure OpenAI connection failed: {e}\")\n"]}, {"cell_type": "markdown", "id": "e00e9a48", "metadata": {}, "source": ["### Configurer le LLM utilisé par Giskard comme \"LLM-as-a-judge\""]}, {"cell_type": "code", "execution_count": 6, "id": "6b19408a", "metadata": {}, "outputs": [], "source": ["giskard.llm.set_llm_model(\"azure/gpt-4o-semarchy\")\n", "giskard.llm.set_embedding_model(\"azure/text-embedding-3-large\")"]}, {"cell_type": "markdown", "id": "4ebdee31", "metadata": {}, "source": ["### Encapsuler l'appel à LI pour Giskard"]}, {"cell_type": "code", "execution_count": null, "id": "48a2d3ab", "metadata": {}, "outputs": [], "source": ["def li_predict(df: pd.DataFrame):\n", "    \"\"\"Wraps the LiveIntelligence LLM call for Giskard testing.\n", "    \n", "    Args:\n", "        df (pd.DataFrame): DataFrame containing questions to be answered (passed by <PERSON><PERSON><PERSON>)\n", "        \n", "    Returns:\n", "        list: List of model responses for each question\n", "    \"\"\"\n", "    def li_internal_call(question: str) -> str:\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": \"You are a helpful assistant and you answer a wide set of questions. Please always be concise and polite.\"},\n", "            {\"role\": \"user\", \"content\": question}\n", "        ]\n", "        response = client.chat.completions.create(\n", "            model=env_liveintel_model,\n", "            messages=messages,\n", "            temperature=0.1,\n", "            max_tokens=150,\n", "            stream=False\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    return [li_internal_call(question) for question in df[\"question\"].values]\n", "\n", "# Create the Giskard model wrapper\n", "giskard_model = giskard.Model(\n", "    model=li_predict,\n", "    model_type=\"text_generation\",\n", "    name=\"LiveIntelligence Alfred-4 QA\",\n", "    description=\"This model uses LiveIntelligence's Alfred-4 to answer general questions\",\n", "    feature_names=[\"question\"]\n", ")"]}, {"cell_type": "markdown", "id": "2fe94e53", "metadata": {}, "source": ["### Tester une complétion classique, sans <PERSON><PERSON><PERSON>, pour valider la fonction de prédiction"]}, {"cell_type": "code", "execution_count": null, "id": "2978f680", "metadata": {}, "outputs": [], "source": ["# Test the wrapped model\n", "test_question_set = pd.DataFrame({\n", "    \"question\": [\"What is the capital of France?\", \"What is 2+2?\"]\n", "})\n", "results = li_predict(test_question_set)\n", "print(results)"]}, {"cell_type": "markdown", "id": "f8e4e746", "metadata": {}, "source": ["### <PERSON><PERSON> and get the results"]}, {"cell_type": "code", "execution_count": null, "id": "a6881068", "metadata": {}, "outputs": [], "source": ["scan_results = giskard.scan(giskard_model)\n", "display(scan_results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}