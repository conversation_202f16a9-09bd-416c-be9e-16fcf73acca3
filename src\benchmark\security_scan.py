"""
Wrapper of <PERSON><PERSON><PERSON> to generate a security scan report. It doesn't use any testset and only targets a model
on the LiveIntelligence API.

It is designed to ask questions and try to trigger some scanners with well known vulnerabilities in LLMs.

"""

import logging
import os
from pathlib import Path
from typing import List
import pandas as pd
import giskard
from api.live_api import ChatCompletions

logger = logging.getLogger(__name__)

class SecurityScanner:
    """
    Class to execute a security scan with <PERSON><PERSON><PERSON> on a LLM model via the LiveIntelligence API.
    """
    def __init__(self) -> None:
        """
        Initialize the configuration from environment variables.
        """
        self.api_key = os.getenv("PROD_KEY")
        self.endpoint = os.getenv("PROD_URL")
        self.model = os.getenv("MODEL_TO_SCAN")
        self.judge_model = os.getenv("JUDGE_MODEL")
        self.judge_embedding_model = os.getenv("JUDGE_EMBEDDING_MODEL")
        if not self.api_key or not self.endpoint or not self.model:
            raise ValueError("PROD_KEY, PROD_URL or MODEL_TO_SCAN not defined in the environment.")
        if not self.judge_model or not self.judge_embedding_model:
            raise ValueError("JUDGE_MODEL or JUDGE_EMBEDDING_MODEL not defined in the environment.")
        self.client = ChatCompletions(self.api_key, self.endpoint, default_model=self.model)

    def _get_answers(self, df: pd.DataFrame) -> List[str]:
        """
        Prediction function for Giskard: takes a DataFrame with a 'question' column,
        calls the LLM via the API and returns a list of answers (str).
        """
        results: List[str] = []
        for question in df["question"].values:
            try:
                response = self.client.execute(messages=[
                    {"role": "user", "content": question}
                ], model=self.model)
                content = ""
                choices = response.get("choices", [])
                if choices and "message" in choices[0]:
                    message = choices[0]["message"]
                    if message.get("role") == "assistant":
                        content = message.get("content", "")
                results.append(content)
            except Exception as e:
                logger.error(f"Error when calling LLM for question '{question}': {e}")
                results.append("")
        return results

    def run(self) -> None:
        """
        Launch the Giskard security scan and generate the HTML report in the 'reports' folder.
        """
        giskard.llm.set_llm_model(self.judge_model)
        giskard.llm.set_embedding_model(self.judge_embedding_model)

        giskard_model = giskard.Model(
            model=self._get_answers,
            model_type="text_generation",
            name="Question answering",
            description="An agent that can answer a wide scope of questions",
            feature_names=["question"],
        )

        results = giskard.scan(giskard_model)
        report_dir = Path("reports")
        report_dir.mkdir(parents=True, exist_ok=True)
        results.to_html(report_dir / "security_report.html")
