{"id":"5d35efd3-2614-4d2a-bdd9-a35c5ac0a7d6","question":"Quels aspects doivent être testés lors de la phase de développement, d’entraînement et de test des modèles ?","reference_answer":"Il faut tester la précision, la robustesse, tout indicateur métier pertinent, la vérification de l’explicabilité, la transparence, les indicateurs statistiques comme l'homogénéité de la variance des résidus, et l’éthique et l’absence de biais.","reference_context":"Document 1012: 3 I Le développement, l’entraînement et le test des modèles\nLa troisième phase est probablement la plus connue et la mieux maîtrisée par les Data Scientists. Elle consiste \ndans un premier temps à procéder au choix judicieux des algorithmes à adopter et à tester, puis à optimiser leur \nparamétrage (souvent effectué par un système de grid search) et à tester les résultats. En cela, elle ne diffère pas \nici fondamentalement de ce qui était fait par le passé avec CRISP .\nNous n’insisterons jamais assez sur l’importance d’inclure tous les cas transmis par les métiers ou détectés lors de \nla phase d’analyse des besoins, sous peine de devoir refaire le travail plusieurs fois. Il faut ainsi tester :\n• La précision ;\n• La robustesse ;\n• T out indicateur métier qui s’avère pertinent au regard du projet concerné ;\n• La vérification de l’explicabilité ;\n• La transparence ;\n• Les indicateurs statistiques comme l'homogénéité de la variance des résidus ;\n• L ’éthique et l’absence de biais.","conversation_history":[],"metadata":{"question_type":"simple","seed_document_id":1012,"topic":"Intelligence artificielle et gestion des données"}}
{"id":"6868959a-822b-4bdf-b250-17c8cc753b28","question":"Quels sont les impacts financiers et techniques de négliger le feature engineering dans les projets MLOps opérés dans le cloud, notamment en termes de coût et de complexité du modèle ?","reference_answer":"Une mauvaise pratique consiste à négliger le feature engineering, ce qui résulte en un modèle lourd, complexe à entraîner, impliquant une phase d'annotation et d’entraînement plus longue, et par conséquent plus consommatrice de ressources. Un modèle plus complexe est nécessairement plus coûteux puisque, dans la plupart des cas, le cloud est payé à l’usage.","reference_context":"Document 1027: Le FinOps associé au MLOps\nOn part du principe que la plupart des projets MLOps seront opérés dans le cloud. Il est ici question de prendre \nen compte le coût de du MLOps et de l’exploitation des modèles d’IA. Une mauvaise pratique, nous l'avons déjà \névoqué, consiste à négliger le feature engineering. Il en résulte alors un modèle lourd, complexe à entraîner, \nqui implique une phase d'annotation et d’entraînement plus longue,  et, par conséquent plus consommatrice  \nde ressources.\nDe même, qui dit modèle plus complexe, dit \nnécessairement modèle plus coûteux puisque, dans la \nplupart des cas, le cloud est payé à l’usage. Une évaluation \ndes coûts du modèle s’impose. Cette évaluation doit se \nfaire à plusieurs niveaux, correspondant à plusieurs \ngrandes étapes :\n• La première, lors de l’élaboration du feature store : \non doit se poser la question de savoir si cela vaut la \npeine de maintenir une feature. Ici, un monitoring \ndes features réellement utilisées dans l’ensemble","conversation_history":[],"metadata":{"question_type":"complex","seed_document_id":1027,"topic":"Intelligence artificielle et gestion des données"}}
{"id":"b8017950-f7f3-40ae-9dfd-a773786c2768","question":"Quels mécanismes étudiés par les neurosciences peuvent inspirer les recherches futures en IA, en tenant compte de la dépendance actuelle aux GAFAM pour les modèles fondamentaux?","reference_answer":"Certains mécanismes étudiés par les neurosciences, comme les premiers modèles de neurone, les modèles hiérarchiques de la vision et les mécanismes d’attention, peuvent donner des idées nouvelles pour les futures recherches en IA.","reference_context":"Document 674: (notamment sur les espaces vectoriels de grandes \ndimensions) pourraient peut-être permettre de pro-\ngresser sur les architectures actuellement utilisées en \nIA. Enfin, si les travaux en apprentissage machine n’ont \npas vocation à reproduire fidèlement le fonctionnement \ndu cerveau humain, certains mécanismes étudiés par \nles neurosciences peuvent donner des idées nouvelles \npour les futures recherches en IA, à l’instar des premiers \nmodèles de neurone, des modèles hiérarchiques de la \nvision et des mécanismes d’attention.\n>>\nSOMMAIRE\n11>>\nSOURCES & GLOSSAIRE P.36-38","conversation_history":[],"metadata":{"question_type":"distracting element","seed_document_id":674,"distracting_context":"Dans le futur on \nrisque de manquer \nde données textuelles \nd’entraînement  \npour les modèles.\nLes modèles fondamentaux dans les mains \ndes GAFAM\nEn augmentant la taille des datasets et en évitant d'avoir \nrecours à de la labellisation massive de données, il a été \npossible de créer les premiers modèles de fondation qui \nont permis ensuite, par affinage successif, de créer de \nl'interactivité proche des interactions humaines grâce à \nla couche InstructGPT. Aujourd’hui, les grands groupes \nque sont OpenAI avec Microsoft, Google ou Meta sont \nles seuls à avoir la puissance du calcul nécessaire pour \nentraîner des modèles fondamentaux multilingues à \nbase de transformers de plus en plus gigantesques. \nVoilà pourquoi les futures évolutions des modèles \nfondamentaux de l’IA générative dépendent en partie \ndes GAFAM.\nIl faut néanmoins se rappeler qu’un modèle incluant \nune pléthore de paramètres toutefois dépourvu de la \ncouche supervisée paraitra relativement inutilisable","topic":"Intelligence artificielle et gestion des données"}}
{"id":"ccf204ea-b586-460c-a243-f020d5ed65b3","question":"Lors de la conférence tech, le conférencier a mentionné l'importance de l'éthique dans le développement de l'IA. Pourriez-vous préciser où se situe l'éthique selon le contexte fourni ?","reference_answer":"L'éthique se situe dans le choix de la data.","reference_context":"Document 412: montrer le plus égalitaire possible entre les différentes populations. Par exemple, si mon IA trie les CV, le data set doit \navoir été choisi dans un data set équitable. Le problème de l’IA aujourd’hui est qu’elle est censée simplifier les processus, \nles décisions… grâce à la data, impliquant alors de disposer d’une data la plus exhaustive possible et ça, c’est compliqué. \nAujourd’hui, faire des algorithmes n’est pas difficile, ce qui est complexe, c’est la data.\nDonc une IA équitable pour le plus grand nombre doit provenir des data bien choisies. Voilà donc où se situe l’éthique : \ndans le choix de la data ! \nLe problème provient des données et des biais qu’elles intègrent car il y a toujours eu des bugs dans les algorithmes \net il y en aura toujours. Mais les biais excluent des individus ce qui oblige alors à montrer ses data sets et à pouvoir y \naccéder en permanence pour corriger les bugs.\nProtégez votre e(thic)-réputation","conversation_history":[],"metadata":{"question_type":"situational","seed_document_id":412,"situational_context":"A tech conference attendee listens attentively to a speaker discussing ethical data sourcing for AI algorithms.","topic":"Intelligence artificielle et gestion des données"}}
{"id":"5a77fe0a-72c8-480e-bd15-397c93ea96f2","question":"Quels sont les problèmes identifiés par Jean Coldefy concernant la connaissance de la demande dans le secteur des transports publics et comment Flux Vision contribue-t-il à améliorer la connaissance des mobilités en France?","reference_answer":"Jean Coldefy identifie des lacunes majeures dans la connaissance de la demande dans le secteur des transports publics, notamment un manque de temporalité et de finesse géographique dans les enquêtes, ainsi qu'une mauvaise prise en compte des nouvelles formes de travail. Flux Vision, grâce au développement de la 4G, de la 5G et des usages, permet d'améliorer cette connaissance en offrant une continuité temporelle et une précision géographique suffisante sur toute la France.","reference_context":"Document 532: Selon lui, la connaissance de la \ndemande dans le secteur des \ntransports publics présente des \nla-cunes majeures. Pour Jean \nColdefy, la mobilité est sans doute \nl’un des seuls secteurs de l’éco-\nnomie où l’on construit de l’offre \nsans connaitre la demande. Les \nenquêtes locales «ménages\/dé-\nplacements» réalisées tous les \ndix ans manquent de temporalité \net de finesse géographique. Les \ndonnées du recensement se foca-\nlisent sur le domicile travail, mais \nprennent mal en compte le temps \npartiel, le télétravail, et les situations \nparticulières (tra-vail en horaires \ndécalés, multi-employeurs, …etc).\nAvec le développement de la 4G, de \nla 5G, et surtout des usages, Flux \nV\nision permet de disposer d’une \ncontinuité temporelle sur toute \n“\nla France avec une précision géographique \nà l’IRIS qui est largement suffisante pour \nla connaissance des mobilités. En effet \nl’immense majorité des kilomètres parcourus \nen France sont le fait de trajets de plus de","conversation_history":[],"metadata":{"question_type":"double","original_questions":[{"question":"Quels sont les problèmes identifiés par Jean Coldefy concernant la connaissance de la demande dans le secteur des transports publics?","answer":"Jean Coldefy identifie des lacunes majeures dans la connaissance de la demande dans le secteur des transports publics. Il souligne que la mobilité est l'un des seuls secteurs de l'économie où l'on construit de l'offre sans connaître la demande. Les enquêtes locales 'ménages\/déplacements' manquent de temporalité et de finesse géographique, et les données du recensement se focalisent sur le domicile travail, mais prennent mal en compte le temps partiel, le télétravail, et les situations particulières."},{"question":"Comment Flux Vision contribue-t-il à améliorer la connaissance des mobilités en France?","answer":"Avec le développement de la 4G, de la 5G, et des usages, Flux Vision permet de disposer d'une continuité temporelle sur toute la France avec une précision géographique à l'IRIS, largement suffisante pour la connaissance des mobilités."}],"seed_document_id":532,"topic":"Mobilité urbaine et données mobiles"}}
{"id":"9fe2c9e7-2147-4d25-9a29-579251f93738","question":"Qui occupe ce poste ?","reference_answer":"Nassima Auvray","reference_context":"Document 792: Dominique Roda, Responsable Communication, Orange Business\n Nassima Auvray, Chief Trust Officer, Orange Business\n Vincent Garnier, Head of Data & AI, Orange Business\nAudrey Lompré, Chargée de Marketing, Digital Services France, Orange Business\n Aurélie Goncalves, Manager Innovation & Stratégie, Digital Services France, Orange Business\n Françoise Soulié-Fogelman, Scientific Advisor – Hub FranceIA\n Yann Le Cun, VP and Chief AI Scientist, Meta\nEn collaboration avec :\nL ’agence Bespoke et Violaine Cherrier\nLes différents articles peuvent être retrouvés en ligne sur le : Blog Business & Decision\nBusiness & Decision\nCœur Défense A , 110 Esplanade Général de Gaulle, 92931 Paris La Défense Cedex\nwww.businessdecision.com\nblog.businessdecision.com\nORANGE BUSINESS SERVICES, société anonyme à conseil d’administration, établie à SAINT-DENIS (93210) sous le SIREN : 345039416. Conception, réalisation : Bespoke – 09\/2023  \nCrédit photo : ©ORANGEBUSINESS, Shutterstock, Pexel\n39\n>>\nSOMMAIRE","conversation_history":[{"role":"user","content":"Je cherche à savoir qui est le Chief Trust Officer chez Orange Business."},{"role":"assistant","content":"How can I help you with that?"}],"metadata":{"question_type":"conversational","seed_document_id":792,"topic":"Intelligence artificielle et gestion des données"}}
