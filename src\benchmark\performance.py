"""
This module provides tools to evaluate various raw aspects of a language model's performance.

Available tests:
- Maximum input tokens (i.e., the largest accepted context window from the Paradigm API)
- Time to completion (3 tokens quantity)
"""

import logging
import os
from api.live_api import Completions, ChatCompletions, DocumentSearch
from tqdm import tqdm
import math
import time
import json


class ModelPerformance:
    """
    Class to evaluate different raw aspects of a model, such as maximum context length and completion times.

    Args:
        model (str): The name or identifier of the model to evaluate.
    """

    def __init__(self, model: str):
        self.model = model

    def test_max_context(self) -> tuple:
        """
        Determines the maximum number of input characters (proxy for tokens) accepted by the model via the completion endpoint.

        The method performs a two-phase search:
        1. Exponential (doubling) search to quickly find an upper bound where the model fails.
        2. Dichotomic (binary) search to precisely identify the maximum accepted input size between the last success and first failure.

        For each successful request, the completion time is measured. The function returns the maximum number of accepted characters and a summary of completion times for low, medium, and high input sizes.

        Returns:
            tuple: (max_accepted_chars, (low, medium, high)), where each of low, medium, high is a tuple (num_chars, completion_time).
            Returns None if required environment variables are not set or no successful completions are recorded.
        """
        logger = logging.getLogger(self.__class__.__name__)
        api_key = os.getenv("PROD_KEY")
        base_url = os.getenv("PROD_URL")
        if not api_key or not base_url:
            logger.debug("PROD_KEY or PROD_URL not defined in the environment.")
            return None
        completions = Completions(api_key, base_url, default_model=self.model)

        # Search parameters
        min_chars = 1000
        max_chars = 1000000
        current_chars = min_chars
        last_success = min_chars
        last_error = None
        tried = set()

        logger.info("Starting max token limit test for model '%s'", self.model)

        # To store (num_chars, completion_time) for each successful request
        completion_times = []

        # Phase 1: double until error
        char_steps = []
        temp_chars = min_chars
        while temp_chars <= max_chars:
            char_steps.append(temp_chars)
            temp_chars = int(temp_chars * 2)

        with tqdm(desc="Max tokens search (phase 1)", total=None) as spinner:
            for current_chars in char_steps:
                prompt = "a" * current_chars
                logger.debug(f"Increasing with {current_chars} characters...")
                try:
                    start_time = time.perf_counter()
                    completions.execute(prompt=prompt, max_tokens=1)
                    elapsed = time.perf_counter() - start_time
                    logger.debug(f"✅ OK")
                    last_success = current_chars
                    tried.add(current_chars)
                    completion_times.append((current_chars, elapsed))
                except Exception as e:
                    logger.debug(
                        f"⚠️ ERROR - Reached a stop point, switching to binary search..."
                    )
                    last_error = current_chars
                    tried.add(current_chars)
                    break
                spinner.update(0)

        if last_error is None:
            logger.debug("✅ Model handles more than %d characters.", max_chars)
            return self._format_completion_time_summary(completion_times)

        # Phase 2: strict binary search (dichotomy) until last_error - last_success == 1
        if last_error is not None:
            max_dicho_steps = int(math.ceil(math.log2(last_error - last_success)))
        else:
            max_dicho_steps = 1
        dicho_pbar = tqdm(total=max_dicho_steps, desc="Dichotomic search (phase 2)")
        while last_error - last_success > 1:
            mid = (last_success + last_error) // 2
            if mid in tried or mid <= last_success or mid >= last_error:
                break
            prompt = "a" * mid
            logger.debug(f"Searching with {mid} characters...")
            try:
                start_time = time.perf_counter()
                completions.execute(prompt=prompt)
                elapsed = time.perf_counter() - start_time
                logger.debug(f"⬆️  Seeking upper limit...")
                last_success = mid
                completion_times.append((mid, elapsed))
            except Exception as e:
                logger.debug(f"⬇️  Seeking lower limit...")
                last_error = mid
            tried.add(mid)
            dicho_pbar.update(1)
        dicho_pbar.close()
        return (last_success, self._format_completion_time_summary(completion_times))

    def _format_completion_time_summary(self, completion_times) -> tuple:
        """
        Summarizes completion times for low, medium, and high input sizes.

        Args:
            completion_times (list): List of tuples (num_chars, completion_time) for each successful request.

        Returns:
            tuple: (low, medium, high), where each is a tuple (num_chars, completion_time).
            Returns None if the input list is empty.
        """
        logger = logging.getLogger(self.__class__.__name__)
        if not completion_times:
            logger.debug("No completion times to summarize.")
            return
        # Sort by number of characters
        completion_times.sort()
        n = len(completion_times)
        low = completion_times[0]
        high = completion_times[-1]
        # Medium: mean of low and high
        medium_chars = int((low[0] + high[0]) / 2)
        medium_time = (low[1] + high[1]) / 2
        medium = (medium_chars, medium_time)
        return (low, medium, high)

    def test_streaming_performance(self, prompt=None) -> dict:
        """
        Tests the streaming performance of the model using ChatCompletions.

        This method measures and collects several performance metrics.

        Args:
            prompt (str, optional): The prompt to use. If None, a default prompt will be used.

        Returns:
            dict: A dictionary containing performance metrics with the following structure:
                {
                    'usage': dict,           # Token usage statistics
                    'text': str,             # Complete generated text
                    'chunk_times': list,     # List of times between chunks
                    'total_time': float,     # Total execution time
                    'chunks_count': int,     # Total number of received chunks
                    'first_token_latency': float  # Time before receiving first token
                }

        Note:
            Requires PROD_KEY, PROD_URL environment variables and a defined model.
            Returns None if these prerequisites are not met.
        """
        logger = logging.getLogger(self.__class__.__name__)
        api_key = os.getenv("PROD_KEY")
        base_url = os.getenv("PROD_URL")
        if not api_key or not base_url or not self.model:
            logger.error("PROD_KEY, PROD_URL or model not defined in the environment.")
            return None
        chat = ChatCompletions(api_key, base_url, self.model)
        if not prompt:
            prompt = "Make me a list of all the things you can do in a very detailed and comprehensive way."
        messages = [{"role": "user", "content": prompt}]
        buffer = ""
        usage = None
        chunk_times = []
        last_time = None
        chunks_count = 0
        first_token_latency = None
        logger.info("Starting. Buffering and counting chunks...")
        start_time = time.perf_counter()
        for chunk in chat.stream(messages):
            now = time.perf_counter()
            if last_time is not None:
                chunk_times.append(now - last_time)
            last_time = now
            choices = chunk.get("choices")
            if isinstance(choices, list) and len(choices) > 0:
                delta = choices[0].get("delta", {})
                content = delta.get("content", "")
                if content:
                    buffer += content
                    chunks_count += 1
                    if first_token_latency is None:
                        first_token_latency = now - start_time
            elif "usage" in chunk:
                usage = chunk["usage"]
            elif "error" in chunk:
                logger.warning(f"Error parsing chunk: {chunk['error']}")
            else:
                logger.warning(f"Unexpected chunk: {chunk}")
        total_time = sum(chunk_times)
        return {
            "usage": usage,
            "text": buffer,
            "chunk_times": chunk_times,
            "total_time": total_time,
            "chunks_count": chunks_count,
            "first_token_latency": first_token_latency,
        }

    def test_document_search_performance(self, workspace_id=None, prompt=None, output_path=None, company_scope=False, private_scope=False) -> dict:
        """
        Tests the response time of the DocumentSearch endpoint.

        Args:
            workspace_id (int, optional): Workspace ID to use. Defaults to 1 if not provided.
            prompt (str, optional): The prompt to use. If None, a default prompt will be used.
            output_path (str, optional): Path to save the raw JSON response. If None, will use 'document_search_response.json'.
            company_scope (bool, optional): Whether to include company scope.
            private_scope (bool, optional): Whether to include private scope.

        Returns:
            dict: A dictionary containing performance metrics with the following structure:
                {
                    'total_time': float,      # Total execution time
                    'response': dict         # Raw API response
                }
        """
        logger = logging.getLogger(self.__class__.__name__)
        api_key = os.getenv("PROD_KEY")
        base_url = os.getenv("PROD_URL")
        if not api_key or not base_url or not self.model:
            logger.error("PROD_KEY, PROD_URL ou model non défini dans l'environnement.")
            return None
        if workspace_id is None:
            workspace_id = 1
        if not prompt:
            prompt = "What's the weather in Paris?"
        if not output_path:
            output_path = "document_search_response.json"
        doc_search = DocumentSearch(api_key, base_url, self.model)
        logger.info(f"Sending a request...")
        start_time = time.perf_counter()
        response = doc_search.execute(
            query=prompt,
            workspace_ids=[workspace_id],
            company_scope=company_scope,
            private_scope=private_scope
        )
        total_time = time.perf_counter() - start_time
        logger.info(f"Response received.")
        # Save raw response
        try:
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(response, f, ensure_ascii=False, indent=2)
            logger.info(f"Raw response saved in {output_path}")
        except Exception as e:
            logger.error(f"Error saving JSON response: {e}")
        return {
            "total_time": total_time,
            "response": response
        }
