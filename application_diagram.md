# Architecture
## Diagramme de flux

```mermaid
flowchart LR
    %% Main Components
    User([👤 Utilisateur]):::user --> App[Application]
    
    %% Main Options
    App --> Menu{Menu Principal}
    Menu -->|Option 1| TestSet([Créer des questions de test])
    Menu -->|Option 2| Security([Contrôler la sécurité comportementale d'un LLM])
    Menu -->|Option 3| RAG([Évaluer la qualité des réponses via RAG])
    
    %% TestSet Flow
    TestSet --> Documents[(Analyser des documents de référence)]:::db
    Documents --> Questions[/Générer des questions/]
    Questions --> TestDB[(Set de questions-test + base de connaissance .csv)]:::db
    
    %% Security Flow
    Security --> SecTest{{Scanners}}
    SecTest --> |Tests|SecDetails[["
        • Divulgation d'informations
        • Injection de prompt
        • Caractères malveillants
        • Contenu inapproprié
        • Stéréotypes
        • Fidélité
        • Format
        • Plausibilité
        • Sycophance
    "]]:::details
    SecDetails --> SecReport>Rapport .html]:::report
    
    %% RAG Flow
    TestDB --> RAG
    RAG --> Simple{{"Tests Giskard"}}
    RAG --> Precision{{"Tests RAGAS"}}
    Simple --> SimpleDetails[["
        • Générateur
        • Retriever
        • Rewriter
        • Router
        • Base de connaissance
    "]]:::details
    Precision --> PrecisionDetails[["
        • Faithfulness
        • Answer relevancy
        • Context precision
        • Context recall
    "]]:::details
    SimpleDetails --> SimpleReport>Rapport .html]:::report
    PrecisionDetails --> DetailReport>Rapport .html + .csv]:::report
    
    %% Styling
    classDef user fill:#6c5ce7,stroke:#a29bfe,stroke-width:2px,color:white
    classDef default fill:#fff,stroke:#333,stroke-width:1px
    classDef db fill:#74b9ff,stroke:#0984e3,stroke-width:2px
    classDef report fill:#00b894,stroke:#00cec9,stroke-width:2px,color:white
    classDef details fill:#ffeaa7,stroke:#fdcb6e,stroke-width:1px
    
    style App fill:#6c5ce7,stroke:#a29bfe,stroke-width:3px,color:white
    style Menu fill:#e84393,stroke:#fd79a8,stroke-width:2px,color:white
    style TestSet fill:#00cec9,stroke:#00b894,stroke-width:2px
    style Security fill:#00cec9,stroke:#00b894,stroke-width:2px
    style RAG fill:#00cec9,stroke:#00b894,stroke-width:2px
    style SecTest fill:#a8e6cf,stroke:#3d84a8,stroke-width:2px
    style Simple fill:#a8e6cf,stroke:#3d84a8,stroke-width:2px
    style Precision fill:#a8e6cf,stroke:#3d84a8,stroke-width:2px
    style Questions fill:#dfe6e9,stroke:#b2bec3,stroke-width:2px

    %% Link Styling
    linkStyle default stroke:#2d3436,stroke-width:2px
```