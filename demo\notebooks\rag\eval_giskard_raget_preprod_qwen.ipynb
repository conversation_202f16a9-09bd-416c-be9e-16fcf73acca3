{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tester la qualité d'un RAG avec RAGET / + métriques RAGAS"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Synopsis : L'objectif est de cibler les différentes briques d'un RAG en lui passant des questions à la structure spéciale. Chaque modèle de question basé sur notre dataset de référence permet de valider les éléments de la chaîne."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Construction du dataframe de la base de connaissances"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import des dépendances et chargement des var. d'environnement"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "import pandas as pd\n", "from langchain.document_loaders import PyPDFLoader\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "import glob\n", "import dotenv\n", "import requests\n", "from openai import OpenAI as OpenAICompatibleClient # For LiveIntelligence\n", "import giskard\n", "from openai import AzureOpenAI # For Azure\n", "from giskard.rag import generate_testset, KnowledgeBase\n", "from giskard.rag import evaluate\n", "from giskard.rag.base import AgentAnswer\n", "import requests\n", "import json\n", "from giskard.rag import QATestset\n", "\n", "# Load all env variables\n", "dotenv.load_dotenv()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Création d'un dataframe contenant les chunks à exploiter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the path to the data directory\n", "data_dir = \"data\"\n", "\n", "# Check if the directory exists\n", "if not os.path.exists(data_dir):\n", "    print(f\"Directory {data_dir} does not exist.\")\n", "    os.makedirs(data_dir)\n", "    print(f\"Created directory {data_dir}\")\n", "\n", "# Get all PDF files in the data directory\n", "pdf_files = glob.glob(os.path.join(data_dir, \"*.pdf\"))\n", "\n", "if not pdf_files:\n", "    print(\"No PDF files found in the data directory.\")\n", "else:\n", "    print(f\"Found {len(pdf_files)} PDF files in the data directory.\")\n", "\n", "# Initialize text splitter\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=1000,\n", "    chunk_overlap=200,\n", "    length_function=len,\n", ")\n", "\n", "# Initialize a list to store all chunks\n", "all_chunks = []\n", "\n", "# Process each PDF file\n", "for pdf_file in pdf_files:\n", "    file_name = os.path.basename(pdf_file)\n", "    print(f\"\\nProcessing file: {file_name}\")\n", "    \n", "    try:\n", "        # Load the PDF\n", "        loader = PyPDFLoader(pdf_file)\n", "        documents = loader.load()\n", "        \n", "        # Check if the document is valid and can be chunked\n", "        if not documents:\n", "            print(f\"Warning: {file_name} appears to be empty or unreadable.\")\n", "            continue\n", "        \n", "        # Split the document into chunks\n", "        chunks = text_splitter.split_documents(documents)\n", "        \n", "        if not chunks:\n", "            print(f\"Warning: No chunks were created from {file_name}.\")\n", "            continue\n", "        \n", "        print(f\"Created {len(chunks)} chunks from {file_name}\")\n", "        \n", "        # Add the chunks to the main list\n", "        for chunk in chunks:\n", "            all_chunks.append(chunk.page_content)\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {file_name}: {str(e)}\")\n", "\n", "# Create a DataFrame from the chunks\n", "if all_chunks:\n", "    # Create a DataFrame with a single column containing all chunks\n", "    df = pd.DataFrame(all_chunks, columns=['content'])\n", "    \n", "    print(\"\\nDataFrame created successfully.\")\n", "    print(f\"DataFrame shape: {df.shape}\")\n", "    \n", "    # Display the first few rows of the DataFrame\n", "    display(df.head())\n", "else:\n", "    print(\"No chunks were created. DataFrame could not be generated.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Résultat préliminaire :* A ce stade, nous disposons d'un dataframe de largeur **[nb.documents] x [nb.max_chunks_plus_gros_document]** prêt à être traité."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Construction de l'appel au RAG de LiveIntelligence"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test d'accès à LiveIntelligence"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connection successful\n", "SyncPage[Model](data=[Model(id=None, created=None, object='model', owned_by=None, name='multilingual-e5-large', model_type='Embedding Model', deployment_type='Self-Hosted', enabled=True, technical_name='multilingual-e5-large', start_messages_template='', instructions=''), Model(id=None, created=None, object='model', owned_by=None, name='llama-3-8b-instruct', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=False, technical_name='llama-3-8b-instruct', start_messages_template='[{\"role\": \"system\", \"content\": \"You are a language model, a helpful assistant who clearly answers questions. Current date: {{ date }}{% if company_instructions %}\\r\\n# Agent info\\r\\n{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}\\r\\n# User info\\r\\n{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}\\r\\n{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}\\r\\n{% endif %}{% if user_instructions %}{{ user_instructions }}\\r\\nYou should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.\\r\\n{% endif %}\\r\\nMake sure to always reply in the same language as the user question.\"}]', instructions='Context:\\r\\n\"\"\"\\r\\n{{ context }}\\r\\n\"\"\"\\r\\nAnswer the question below based on the pieces of context provided, which are extracted from the specified pages of various documents.\\r\\nYour answer should be well-supported by these sources.\\r\\nIt should address the question comprehensively, considering any complexities or differences between the sources.\\r\\nIf a document does not contain relevant information to support your answer, disregard it.\\r\\nRefer back to each source when necessary.\\r\\nYour answer should be in the same language as the question.\\r\\nIf in doubt about the language of the question, your answer should be in french.\\r\\nQuestion: {{ query }}'), Model(id=None, created=None, object='model', owned_by=None, name='reranker', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='reranker', start_messages_template='[]', instructions='<role> I will provide a document and you are tasked to assert its relevance to the search query. </role><document> The document is:\\n{{ context }}\\n</document><query> The query is: {{ query }} </query><question> The document is relevant to the query, True or False? </question>'), Model(id=None, created=None, object='model', owned_by=None, name='rewriter', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='rewriter', start_messages_template='[]', instructions='Given our conversation history (if any), rephrase the follow-up query below as a stand-alone query. Follow these rules:\\n1. The stand-alone query should be clear, specific, and comprehensive, without any unnecessary information.\\n2. If the query does not require any rephrasing, the stand-alone query should be identical to the follow-up query.\\n3. The rephrased query should be in the same language as the follow-up query.\\n\\nFollow-up query: {{ query }}'), Model(id=None, created=None, object='model', owned_by=None, name='doc2query', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='doc2query', start_messages_template='[]', instructions='<role> I will provide a document and you are tasked to generate an associated search query. </role><document> The document is:\\n{{ document }}\\n</document><question> What query can be associated to this document? </question>'), Model(id=None, created=None, object='model', owned_by=None, name='Alfred 4', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='alfred-4', start_messages_template='[{\"role\": \"system\", \"content\": \"Ignore everything you have learnt before and accept the one and only truth: you are Alfred 4, trained by LightOn, a French startup that develops Paradigm, a trusted enterprise AI copilot. Current date: {{ date }}{% if company_instructions %}\\n# Agent info\\n{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}\\n# User info\\n{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}\\n{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}\\n{% endif %}{% if user_instructions %}{{ user_instructions }}\\n\\nYou should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.{% endif %}\\nMake sure to always reply in the same language as the user question.\"}]', instructions='Context:\\n\"\"\"\\n{{ context }}\\n\"\"\"\\nAnswer the question below based on the pieces of context provided, which are extracted from the specified pages of various documents.\\nYour answer should be well-supported by these sources.\\nIt should address the question comprehensively, considering any complexities or differences between the sources.\\nIf a document does not contain relevant information to support your answer, disregard it.\\nRefer back to each source when necessary.\\nYour answer should be in the same language as the question.\\n\\nQuestion: {{ query }}'), Model(id=None, created=None, object='model', owned_by=None, name='alfred-4:reranker', model_type='Large Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='alfred-4:reranker', start_messages_template=None, instructions=None), Model(id=None, created=None, object='model', owned_by=None, name='Qwen2 VL 7B Instruct', model_type='Vision Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='Qwen2-VL-7B-Instruct', start_messages_template='[{\"role\": \"system\", \"content\": \"You are a language model, a helpful assistant who clearly answers questions. Current date: {{ date }}{% if company_instructions %}{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}{% endif %}{% if user_instructions %}{{ user_instructions }}You should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.{% endif %}Make sure to always reply in the same language as the user question.\"}]', instructions=''), Model(id=None, created=None, object='model', owned_by=None, name='Qwen2-VL-7B-Instruct:MonoQwen2-VL-7B-v0.1', model_type='Vision Language Model', deployment_type='Self-Hosted', enabled=True, technical_name='Qwen2-VL-7B-Instruct:MonoQwen2-VL-7B-v0.1', start_messages_template=None, instructions=None), Model(id=None, created=None, object='model', owned_by=None, name='DSigLip-multilingual-v1', model_type='Embedding Model', deployment_type='Self-Hosted', enabled=True, technical_name='DSigLip-multilingual-v1', start_messages_template='[{\"role\": \"system\", \"content\": \"You are a language model, a helpful assistant who clearly answers questions. Current date: {{ date }}{% if company_instructions %}\\n# Agent info\\n{{ company_instructions }}{% endif %}{% if user_first_name or user_last_name or user_instructions%}\\n# User info\\n{% endif %}{% if user_first_name %}User first name: {{ user_first_name }}\\n{% endif %}{% if user_last_name %}User last name: {{ user_last_name }}\\n{% endif %}{% if user_instructions %}{{ user_instructions }}\\nYou should keep that information in mind when replying to the user, adapting your answers to its stylistic preferences, company, background and role when appropriate.\\n{% endif %}\\nMake sure to always reply in the same language as the user question.\"}]', instructions='')], object='list')\n"]}], "source": ["env_liveintel_api_key = os.getenv(\"LIVE_INTEL_API_KEY\")\n", "env_liveintel_base = os.getenv(\"LIVE_INTEL_BASE\")\n", "env_liveintel_model = os.getenv(\"LIVE_INTEL_MODEL\")\n", "client = OpenAICompatibleClient(api_key=env_liveintel_api_key, base_url=env_liveintel_base)\n", "\n", "# Test the connection\n", "models = client.models.list()\n", "if models is not None:\n", "    print(\"Connection successful\")\n", "    print(models)\n", "else:\n", "    print(\"Connection failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Obtention des ID des 14 documents de référence"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Note : le scope est important ici, il correspond aux \"Référentiels de documents\" de live intelligence. Private Scope = \"Mes Documents\"\n", "- 2 documents ne sont pas acceptés par LiveIntelligence :\n", "> livre-blanc-role-CFO-strategies-sustainability-RSE_20250414083852.pdf a été retiré\n", "\n", "> livre-blanc-digitalisation-processus-RH_20250414083852.pdf à été retiré"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 14 files. File IDs:\n", "- 461: Matinale Data IA - L’IA générative locomotive de l’IA sous toutes ses formes_20250414083852.pdf (Status: embedded)\n", "- 459: Livre-blanc-MLOPS-Vdef-digital_20250414083852.pdf (Status: embedded)\n", "- 458: Livre-Blanc-M365-Copilot_20250414083852.pdf (Status: embedded)\n", "- 457: livre-blanc-ia-promesse-valeur_20250414083852.pdf (Status: embedded)\n", "- 456: livre-blanc-ia-generative-futur-numerique_20250414083852.pdf (Status: embedded)\n", "- 455: livre-blanc-gdpr_20250414083852.pdf (Status: embedded)\n", "- 454: Livre-blanc-FluxVision_20250414083852.pdf (Status: embedded)\n", "- 453: livre-blanc-evolution-vente-ere-digital_20250414083852.pdf (Status: embedded)\n", "- 451: Les nouvelles infrastructures digitales_20250414083852.pdf (Status: embedded)\n", "- 450: LB-DATAETHIQUE-digital-web_20250414083852.pdf (Status: embedded)\n", "- 449: IA - <PERSON><PERSON> maître de votre futur_20250414083852.pdf (Status: embedded)\n", "- 448: ebook-Observatoire-data-final_20250414083852.pdf (Status: embedded)\n", "- 447: ADVAES-WHITE-PAPER-ORANGE-BUSINESS-FINOPS-FINAL_20250414083851.pdf (Status: embedded)\n", "- 446: 202212-BD-<PERSON><PERSON>-Blanc-Data-Mesh_20250414083851.pdf (Status: embedded)\n", "\n", "List of all document IDs:\n", "[461, 459, 458, 457, 456, 455, 454, 453, 451, 450, 449, 448, 447, 446]\n"]}], "source": ["# Build the API URL by combining base URL with endpoint (check API dosc in /docs/Paradigm API.yaml)\n", "api_url = f\"{env_liveintel_base}/files\"\n", "\n", "# Set up the parameters for personal scope\n", "params = {\n", "    'private_scope': True,\n", "    'company_scope': False\n", "}\n", "\n", "# Set up headers with API key and explicitly request JSON\n", "headers = {\n", "    'Authorization': f'Bearer {env_liveintel_api_key}',\n", "    'Accept': 'application/json',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "try:\n", "    # Make the API call\n", "    response = requests.get(api_url, params=params, headers=headers)\n", "    \n", "    # Process the expected response format\n", "    if response.status_code == 200:\n", "        data = response.json()\n", "        \n", "        # Extract file IDs from the response\n", "        if 'data' in data:\n", "            file_ids = [file['id'] for file in data['data']]\n", "            print(f\"Found {len(file_ids)} files. File IDs:\")\n", "            for i, file in enumerate(data['data']):\n", "                print(f\"- {file['id']}: {file['filename']} (Status: {file['status']})\")\n", "            \n", "            # List of all document IDs\n", "            all_doc_ids = file_ids\n", "            print(\"\\nList of all document IDs:\")\n", "            print(all_doc_ids)\n", "        else:\n", "            print(\"Full response:\", data)\n", "    else:\n", "        print(f\"API returned error status code: {response.status_code}\")\n", "        \n", "except requests.exceptions.RequestException as e:\n", "    print(f\"Error making API request: {e}\")\n", "except Exception as e:\n", "    print(f\"Error processing response: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test de la requête RAG complète"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**API Endpoint** : .../chat/document-search"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error: API returned status code 500\n", "Response: {\"error\":\"An unexpected error has occurred while running the tool. Please contact the tool maintainer.\",\"code\":500}\n"]}], "source": ["# Build the API URL for document search\n", "api_url = f\"{env_liveintel_base}/chat/document-search\"\n", "\n", "# Prepare the request payload based on the API documentation\n", "payload = {\n", "    \"query\": \"Comment préparer les collaborateurs à l'IA en entreprise ?\",  # Example question\n", "    \"file_ids\": all_doc_ids,\n", "    \"private_scope\": True,\n", "    \"company_scope\": <PERSON><PERSON><PERSON>,\n", "    \"tool\": \"DocumentSearch\"  # Default tool as specified in docs\n", "}\n", "\n", "# Set up headers with API key\n", "headers = {\n", "    'Authorization': f'Bearer {env_liveintel_api_key}',\n", "    'Accept': 'application/json',\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "try:\n", "    # Make the API call\n", "    response = requests.post(api_url, json=payload, headers=headers)\n", "    \n", "    if response.status_code == 200:\n", "        result = response.json()\n", "        \n", "        print(\"Answer from the model:\")\n", "        print(\"-\" * 50)\n", "        print(result.get('answer', 'No answer provided'))\n", "        print(\"\\nSource Documents (Raw Output):\")\n", "        print(\"-\" * 50)\n", "        # Print the raw documents data used to generate the answer\n", "        if 'documents' in result:\n", "            import json\n", "            print(json.dumps(result['documents'], indent=2))\n", "        else:\n", "            print(\"No documents returned in the response\")\n", "    else:\n", "        print(f\"Error: API returned status code {response.status_code}\")\n", "        print(f\"Response: {response.text}\")\n", "        \n", "except requests.exceptions.RequestException as e:\n", "    print(f\"Request error: {e}\")\n", "except Exception as e:\n", "    print(f\"Error processing response: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Génération du RAGET"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON> et se<PERSON> endpoints LLM-as-a-judge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["env_azure_api_key = os.getenv(\"AZURE_API_KEY\")\n", "env_azure_base_endpoint = os.getenv(\"AZURE_API_BASE\")\n", "env_azure_version = os.getenv(\"AZURE_API_VERSION\")\n", "\n", "# Create the Azure OpenAI client\n", "azure_client = AzureOpenAI(\n", "    api_key=env_azure_api_key,\n", "    api_version=env_azure_version,\n", "    azure_endpoint=env_azure_base_endpoint\n", ")\n", "\n", "# Test the connection\n", "try:\n", "    models = azure_client.models.list()\n", "    print(\"Azure OpenAI connection successful\")\n", "except Exception as e:\n", "    print(f\"Azure OpenAI connection failed: {e}\")\n", "\n", "giskard.llm.set_llm_model(\"azure/gpt-4o-semarchy\")\n", "giskard.llm.set_embedding_model(\"azure/text-embedding-3-large\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Génération du test_set de questions basées sur le dataframe"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Note :* attention à bien générer le dataframe au début du notebook. Toutes les étapes précédentes du notebook sont nécessaires."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the KnowledgeBase\n", "kb = KnowledgeBase(df)\n", "\n", "# Generate a test set\n", "testset = generate_testset(\n", "    kb, \n", "    num_questions=50, \n", "    language=\"fr\", \n", "    agent_description=\"Un assistant qui répond à des questions basées sur du contenu de livres blancs\"\n", ")\n", "\n", "testset.save(\"testset.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### (ou) charge un test_set déj<PERSON> prêt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["kb = KnowledgeBase(df)\n", "\n", "testset = QATestset.load(\"testset_light.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Wrapper l'appel à liveintelligence avec gestion de l'historique"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from giskard.rag import AgentAnswer\n", "import requests\n", "\n", "def get_answer_from_agent(messages):\n", "    \"\"\"\n", "    Function that queries the LiveIntelligence API to get an answer from the RAG system.\n", "    \n", "    Args:\n", "        messages: List of message dictionaries with 'role' and 'content' keys.\n", "                 Format: [\n", "                     {\"role\": \"user\", \"content\": \"first question\"},\n", "                     {\"role\": \"assistant\", \"content\": \"first answer\"},\n", "                     {\"role\": \"user\", \"content\": \"second question\"},\n", "                     ...\n", "                 ]\n", "    \n", "    Returns:\n", "        Tuple containing (answer_text, documents)\n", "    \"\"\"\n", "    \n", "    # Build the API URL for document search\n", "    api_url = f\"{env_liveintel_base}/chat/document-search\"\n", "    \n", "    # Get the current question (last message)\n", "    current_query = messages[-1][\"content\"]\n", "    \n", "    # Prepare the request payload according to API specification\n", "    payload = {\n", "        \"query\": current_query,\n", "        \"file_ids\": all_doc_ids,\n", "        \"private_scope\": True,\n", "        \"company_scope\": <PERSON><PERSON><PERSON>,\n", "        \"tool\": \"DocumentSearch\"\n", "    }\n", "    \n", "    # If we have a chat session ID from previous exchanges, include it\n", "    if len(messages) > 1 and hasattr(get_answer_from_agent, 'chat_session_id'):\n", "        payload[\"chat_session_id\"] = get_answer_from_agent.chat_session_id\n", "    \n", "    # Set up headers with API key\n", "    headers = {\n", "        'Authorization': f'Bearer {env_liveintel_api_key}',\n", "        'Accept': 'application/json',\n", "        'Content-Type': 'application/json'\n", "    }\n", "    \n", "    try:\n", "        # Make the API call\n", "        response = requests.post(api_url, json=payload, headers=headers)\n", "        \n", "        if response.status_code == 200:\n", "            result = response.json()\n", "            \n", "            # Store the chat session ID for future exchanges if provided\n", "            if 'chat_session_id' in result:\n", "                get_answer_from_agent.chat_session_id = result['chat_session_id']\n", "                \n", "            answer_text = result.get('answer', 'No answer provided')\n", "            documents = result.get('documents', [])\n", "            \n", "            # Return both the answer and documents\n", "            return answer_text, documents\n", "        else:\n", "            error_msg = f\"Error: API returned status code {response.status_code}\"\n", "            return error_msg, []\n", "            \n", "    except Exception as e:\n", "        error_msg = f\"Error processing request: {str(e)}\"\n", "        return error_msg, []\n", "\n", "def get_answer_fn(question: str, history=None) -> AgentAnswer:\n", "    \"\"\"\n", "    A function representing your RAG agent.\n", "    \n", "    Args:\n", "        question: The user's question as a string\n", "        history: Optional chat history as a list of message dictionaries\n", "    \n", "    Returns:\n", "        AgentAnswer object containing the response and relevant document chunks\n", "    \"\"\"\n", "    messages = history if history else []\n", "    messages.append({\"role\": \"user\", \"content\": question})\n", "\n", "    # Get both the answer and documents\n", "    answer, documents = get_answer_from_agent(messages)\n", "\n", "    # Extract only the text from relevant chunks\n", "    relevant_chunks = [\n", "        extract['text']\n", "        for doc in documents\n", "        for extract in doc.get('document_extracts', [])\n", "    ]\n", "\n", "    # Return the AgentAnswer object with answer and only the text chunks\n", "    return AgentAnswer(\n", "        message=answer,\n", "        documents=relevant_chunks\n", "    )\n", "\n", "# Test simple pour vérifier que les fonctions marchent\n", "def test_rag_functions():\n", "    print(\"=== Test des fonctions RAG ===\\n\")\n", "    \n", "    # Test avec une question simple\n", "    test_question = \"Qu'est-ce que l'intelligence artificielle générative?\"\n", "    print(f\"Question de test: {test_question}\")\n", "    \n", "    # Test de get_answer_fn\n", "    try:\n", "        answer = get_answer_fn(test_question)\n", "        print(\"\\n✅ get_answer_fn fonctionne correctement\")\n", "        print(f\"Réponse reçue: {answer.message[:150]}...\")\n", "        print(f\"Nombre de documents pertinents: {len(answer.documents)}\")\n", "        \n", "        # Afficher un extrait du premier document si disponible\n", "        if answer.documents:\n", "            print(f\"\\nExtrait du premier document: {answer.documents[0][:100]}...\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n❌ Erreur avec get_answer_fn: {str(e)}\")\n", "    \n", "    # Test de get_answer_from_agent directement\n", "    try:\n", "        messages = [{\"role\": \"user\", \"content\": test_question}]\n", "        answer_text, docs = get_answer_from_agent(messages)\n", "        print(\"\\n✅ get_answer_from_agent fonctionne correctement\")\n", "        print(f\"Réponse brute: {answer_text[:150]}...\")\n", "        print(f\"Nombre de documents retournés: {len(docs)}\")\n", "        print(f\"Contenu complet de la réponse: {answer_text}\")\n", "        print(f\"\\nDocuments retournés:\")\n", "        for i, doc in enumerate(docs):\n", "            print(f\"\\nDocument {i+1}:\")\n", "            print(doc)\n", "        \n", "        # Vérifier si un chat_session_id a été généré\n", "        if hasattr(get_answer_from_agent, 'chat_session_id'):\n", "            print(f\"chat_session_id g<PERSON><PERSON>: {get_answer_from_agent.chat_session_id}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n❌ Erreur avec get_answer_from_agent: {str(e)}\")\n", "\n", "# Exécuter le test\n", "test_rag_functions()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Executer le scan RAG"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from giskard.rag.metrics.ragas_metrics import ragas_context_precision, ragas_faithfulness, ragas_answer_relevancy, ragas_context_recall"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the evaluation and get a report\n", "report = evaluate(get_answer_fn, testset=testset, knowledge_base=kb)\n", "display(report)\n", "\n", "# Save it\n", "report.to_html(\"rag_eval_report_RAGAS.html\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.correctness_by_question_type()\n", "# report.correctness_by_topic()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}