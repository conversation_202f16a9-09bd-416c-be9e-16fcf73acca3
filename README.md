# Giskard LLM & RAG Benchmarking Tool

A wrapper app for benchmarking Large Language Models (LLMs) and Retrieval-Augmented Generation (RAG) systems in **Live Intelligence Trust**. It integrates various tools like RAGAS, Giskard, and LiteLLM, primarily utilizing Azure OpenAI services for evaluation. Includes a custom API integration with Live Intelligence's Paradigm API.

> ⚠️ **WARNING**
> 
> This underlying custom API is still using 'query' to retrieve information from files and performs a 'completions' to simulate the RAG system. It is a temp solutions to avoid some missing endpoints not yet ready in production. Otherwise, we would not have been able to test the RAG.

## Prerequisites

*   Python 3.12+
*   Conda (Recommended for environment management)
*   Access to an Azure OpenAI instance with deployed models (GPT-4 and embedding models).
*   Access to the LiveIntelligence API, with an api key, a workspace to stores docs inside (be an admin) and some docs to be chunked for generating test questions.

## Setup

1.  **Clone the repository:**
    ```bash
    git clone <your-repository-url>
    cd giskard-private
    ```

2.  **Create and activate Conda environment:**
    ```bash
    conda create --name giskard-env python=3.12  # Or your preferred Python version
    conda activate giskard-env
    ```

3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Configure environment variables:**
    Create a `.env` file in the root directory with this structure:

    ```dotenv
    # --- Azure OpenAI Configuration ---
    # Used by RAGAS and potentially other SDKs directly interacting with Azure OpenAI

    # Your Azure OpenAI resource endpoint
    AZURE_OPENAI_ENDPOINT="https://<your-azure-openai-resource-name>.openai.azure.com"
    # API Version for Azure OpenAI (check Azure documentation for current valid versions)
    OPENAI_API_VERSION="2024-12-01-preview"
    # API Key for your Azure OpenAI resource
    AZURE_OPENAI_API_KEY="<your-azure-openai-api-key>"

    # --- LiteLLM Azure Configuration ---
    # Used by LiteLLM for unified access to LLMs, including Azure deployments.
    # Duplication is necessary due to differing naming conventions between libraries.

    # API Key for Azure (same as AZURE_OPENAI_API_KEY)
    AZURE_API_KEY="<your-azure-openai-api-key>"
    # API Base URL for Azure (same as AZURE_OPENAI_ENDPOINT)
    AZURE_API_BASE="https://<your-azure-openai-resource-name>.openai.azure.com"
    # API Version for Azure (same as OPENAI_API_VERSION)
    AZURE_API_VERSION="2024-12-01-preview"

    # --- Deployment & Model Names ---

    # Azure deployment name for the main generation model (e.g., GPT-4.1) used by RAGAS
    AZURE_OPENAI_DEPLOYMENT_NAME="<your-gpt4-deployment-name>"
    # Azure deployment name for the embedding model (e.g., text-embedding-3-large) used by RAGAS
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT="<your-embedding-deployment-name>"

    # Models used by Giskard scans, specified in LiteLLM format: "azure/<deployment-name>"
    # Ensure these deployments exist in your Azure OpenAI resource.
    JUDGE_EMBEDDING_MODEL="azure/<your-embedding-deployment-name>"
    JUDGE_MODEL="azure/<your-gpt4-deployment-name>"

    # --- LiveIntelligence API Configuration (Optional) ---
    # Required only if running tests involving the LiveIntelligence platform.

    # API Key for LiveIntelligence (Prod or Preprod)
    PROD_KEY="<your-liveintelligence-api-key>"
    # URL for LiveIntelligence API (Prod or Preprod)
    PROD_URL="<your-liveintelligence-api-url>" # e.g., https://liveintelligence.orange-business.com/ or https://trust.pprd.liveintelligence.orange-business.com/

    # The technical name of the LiveIntelligence model to scan (e.g., "alfred-4.1", "llama-3-8b-instruct")
    # Ensure the model is enabled in the LiveIntelligence platform.
    MODEL_TO_SCAN="<liveintelligence-model-technical-name>"

    # Workspace ID in LiveIntelligence containing the documents for RAG testing.
    # Find this ID in the URL when navigating to the workspace admin panel
    # (e.g., .../workspace/50/change/ -> WORKSPACE_TO_SCAN="50").
    # IMPORTANT: The documents in this workspace MUST match the documents placed in the `knowledge/` directory of this project.
    WORKSPACE_TO_SCAN="<liveintelligence-workspace-id>"

    # Scope for document search within LiveIntelligence. "True" usually means documents uploaded by the user associated with the API key.
    PRIVATE_SCOPE="True"

    ```

## Running the Application

Simply run the `src/app.py` file and let the app guide you.

**Note that for RAG scans, you will need to build a question testset first.**
For this, 
- Place your reference documents (txt, html, md, doc or pdf) in the `knowledge/` directory for the app to parse them.
- Upload the same docs in a dedicated workspace in Live Intelligence. You will need admin rights to do this.
- Ensure your are passing the good workspace ID to the corresponding env variable.

## Features

This application facilitates the evaluation of LLMs and RAG systems through various scans and metrics, generating reports stored in the `reports/` directory.

### Implemented Scans & Metrics

1.  **RAGAS Evaluation:**
    *   Utilizes the RAGAS library for RAG pipeline evaluation.
    *   Likely measures metrics such as:
        *   `Faithfulness`: How factually accurate the generated answer is based on the retrieved context.
        *   `Answer Relevancy`: How relevant the answer is to the original question.
        *   `Context Precision`: Signal-to-noise ratio of the retrieved contexts.
        *   `Context Recall`: Ability of the retriever to fetch all necessary information.
        *   `Answer Semantic Similarity`: Semantic resemblance between the generated answer and the ground truth answer (if available).
    *   Requires Azure deployments specified by `AZURE_OPENAI_DEPLOYMENT_NAME` and `AZURE_OPENAI_EMBEDDING_DEPLOYMENT`.

2.  **Giskard LLM Evaluation:**
    *   Uses the Giskard framework for broader LLM testing.
    *   Scans include (+ others):
        *   **Security Scans:** Detecting vulnerabilities like prompt injection, harmful content generation, etc.
        *   **Robustness Tests:** Evaluating performance against typos, perturbations, etc.
        *   **Bias Evaluation:** Checking for social or demographic biases.
        *   **RAG-specific Scans:** Evaluating hallucination, information retrieval quality within the Giskard framework.
    *   Requires Azure deployments specified by `JUDGE_MODEL` and `JUDGE_EMBEDDING_MODEL` in LiteLLM format.

### Reporting

RAG eval results and benchmarks are saved in the `reports/` directory. You will find .html reports and .csv for details (RAGAS only)

## VLM Benchmarking

Another module is included for evaluating Vision Language Models (VLM) via the `VisionRAGScan` script. This module allows benchmarking of RAG systems capable of processing both text and images, using the InfographicsVQA dataset.

### How the VLM RAG Scan module works

- **Data download and preparation**: The module automatically downloads a subset of the InfographicsVQA dataset, extracts images, and generates a JSON file containing question/answer pairs associated with each image.
- **Image upload**: Images are then uploaded to the target workspace of the Live Intelligence platform via the API.
- **Request generation and execution**: For each question, an asynchronous request is sent to the DocumentSearch or VisionDocumentSearch tooling chain, depending on the chosen pipeline (classic or OCR).
- **Automatic evaluation**: The model's answers are automatically evaluated using a DSPy judge, which compares the model's answer to the expected answers from the dataset.
- **Reporting**: Detailed results (questions, answers, judgments, retrieved images, etc.) are exported to a CSV file, with a summary of accuracy and recall scores.

#### Usage example

Launch the VLM scan from the main application menu (`src/app.py`). The module will guide you to:
- Choose the number of questions to test
- Select the pipeline (DocumentSearch or VisionDocumentSearch)
- Upload images to the workspace
- Run the benchmark and generate the CSV report

The associated research demo notebook can be found in `demo/notebooks/vlm/vision_bench.ipynb`.
