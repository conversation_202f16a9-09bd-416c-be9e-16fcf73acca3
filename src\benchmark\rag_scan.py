"""
Module providing an object-oriented interface to evaluate RAG (Retrieval-Augmented Generation) systems using two approaches:

- Simple RAG Scan (Giskard)
- Precision RAG Scan (RAGAS)

All configuration (API keys, model names, paths) is handled via instance attributes or environment variables.
"""

import logging
import os
import time
from pathlib import Path
import sys
from typing import List, Optional, Any

sys.path.append(str(Path(__file__).parent.parent))

from api.live_api import DocumentSearchAlt
import pandas as pd
import giskard
from giskard.rag import QATestset, KnowledgeBase, AgentAnswer

# RAGAS Precision scan supplementary imports
from ragas import evaluate, EvaluationDataset
from ragas.dataset_schema import SingleTurnSample
from ragas.metrics import Faithfulness, AnswerRelevancy, ContextPrecision, ContextRecall
from langchain_community.chat_models import AzureChatOpenAI
from ragas.llms import LangchainLLMWrapper
from langchain_openai import AzureOpenAIEmbeddings
from ragas.embeddings import LangchainEmbeddingsWrapper


class RAGScan:
    """
    Main class to perform RAG scans (simple and precision) on a retrieval-augmented generation system.
    """
    def __init__(
        self,
        knowledge_dir: str = "knowledge",
        reports_dir: str = "reports",
        judge_model: Optional[str] = None,
        judge_embedding_model: Optional[str] = None,
        prod_key: Optional[str] = None,
        prod_url: Optional[str] = None,
        model_to_scan: Optional[str] = None,
        workspace_to_scan: Optional[str] = None,
        private_scope: Optional[str] = None,
        azure_openai_api_key: Optional[str] = None,
        azure_openai_endpoint: Optional[str] = None,
        openai_api_version: Optional[str] = None,
        azure_openai_deployment: Optional[str] = None,
        azure_openai_embedding_deployment: Optional[str] = None,
        logger: Optional[logging.Logger] = None,
    ):
        """
        Initialize the RAG scan configuration from arguments or environment variables.
        """
        self.knowledge_dir = Path(knowledge_dir)
        self.reports_dir = Path(reports_dir)
        self.judge_model = judge_model or os.getenv("JUDGE_MODEL")
        self.judge_embedding_model = judge_embedding_model or os.getenv("JUDGE_EMBEDDING_MODEL")
        self.prod_key = prod_key or os.getenv("PROD_KEY")
        self.prod_url = prod_url or os.getenv("PROD_URL")
        self.model_to_scan = model_to_scan or os.getenv("MODEL_TO_SCAN")
        self.workspace_to_scan = workspace_to_scan or os.getenv("WORKSPACE_TO_SCAN")
        self.private_scope = private_scope or os.getenv("PRIVATE_SCOPE")
        self.azure_openai_api_key = azure_openai_api_key or os.getenv("AZURE_OPENAI_API_KEY")
        self.azure_openai_endpoint = azure_openai_endpoint or os.getenv("AZURE_OPENAI_ENDPOINT")
        self.openai_api_version = openai_api_version or os.getenv("OPENAI_API_VERSION")
        self.azure_openai_deployment = azure_openai_deployment or os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        self.azure_openai_embedding_deployment = azure_openai_embedding_deployment or os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT")
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.logger.debug("RAGScan initialized with knowledge_dir=%s, reports_dir=%s", self.knowledge_dir, self.reports_dir)

    def _get_answer_with_sources(self, question: str, history: Optional[List[dict]] = None) -> AgentAnswer:
        """
        Utility method to query the LiveIntelligence API and get an agent answer with sources.
        """
        if not self.prod_key or not self.prod_url or not self.model_to_scan:
            raise ValueError("PROD_KEY, PROD_URL or MODEL_TO_SCAN not defined.")

        user_prompt = ""
        if history:
            for msg in history:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                if role == "user":
                    user_prompt += f"[user]: {content}\n"
                elif role == "assistant":
                    user_prompt += f"[assistant]: {content}\n"
        user_prompt += f"[user]: {question}"

        client = DocumentSearchAlt(self.prod_key, self.prod_url, default_model=self.model_to_scan)
        try:
            response = client.execute(
                query=user_prompt,
                model=self.model_to_scan,
                workspace_ids=[int(self.workspace_to_scan)] if self.workspace_to_scan else None,
                private_scope=self.private_scope,
            )
            content = ""
            choices = response.get("response", {}).get("choices", [])
            if choices and "message" in choices[0]:
                message = choices[0]["message"]
                if message.get("role") == "assistant":
                    content = message.get("content", "")
            documents = []
            for chunk_set in response.get("source_chunks", []):
                for chunk in chunk_set.get("chunks", []):
                    doc_text = chunk.get("text", "")
                    if doc_text:
                        documents.append(doc_text)
            return AgentAnswer(message=content, documents=documents)
        except Exception as e:
            self.logger.error(f"Error when calling the RAG API for question '{question}': {e}")
            return AgentAnswer(message="", documents=[])

    def run_simple_scan(self, report_path: Optional[str] = None) -> None:
        """
        Execute a simple correctness scan with Giskard and save the HTML report.
        """
        report_path = report_path or str(self.reports_dir / "scan_report_simple.html")
        giskard.llm.set_llm_model(self.judge_model)
        giskard.llm.set_embedding_model(self.judge_embedding_model)

        testset_path = self.knowledge_dir / "testset.jsonl"
        kb_path = self.knowledge_dir / "knowledge_chunks.csv"
        if not testset_path.exists():
            self.logger.error("File testset.jsonl not found in the knowledge directory.")
            return
        if not kb_path.exists():
            self.logger.error("File knowledge_chunks.csv not found in the knowledge directory.")
            return

        testset = QATestset.load(testset_path)
        df = pd.read_csv(kb_path)
        knowledge_base = KnowledgeBase.from_pandas(df=df, columns=["content"])

        self.logger.info("Starting Giskard correctness scan (simple)...")
        report = giskard.rag.evaluate(
            self._get_answer_with_sources, testset=testset, knowledge_base=knowledge_base
        )
        try:
            report.to_html(report_path)
            self.logger.info(f"Scan complete. Report saved to {Path(report_path).resolve()}")
        except Exception as e:
            self.logger.error(f"Error while saving the report: {e}")

    def run_precision_scan(
        self,
        report_path_csv: Optional[str] = None,
        report_path_html: Optional[str] = None,
        report_template_path: Optional[str] = None,
    ) -> None:
        """
        Execute a precision scan with RAGAS and save the CSV and HTML reports.
        """
        report_path_csv = report_path_csv or str(self.reports_dir / "scan_report_precision_ragas.csv")
        report_path_html = report_path_html or str(self.reports_dir / "scan_report_precision_ragas.html")
        report_template_path = report_template_path or str(self.reports_dir / "templates" / "ragas_metrics.html")

        testset_path = self.knowledge_dir / "testset.jsonl"
        if not testset_path.exists():
            self.logger.error("File testset.jsonl not found in knowledge.")
            return

        testset = pd.read_json(testset_path, lines=True)
        samples = []
        for idx, row in testset.iterrows():
            question = row["question"]
            reference = row["reference_answer"]
            conversation_history = row.get("conversation_history", [])
            self.logger.info(f"Generating answers ... ({len(testset) - idx} questions remaining)")
            agent_answer = self._get_answer_with_sources(question, history=conversation_history)
            response = agent_answer.message
            retrieved_contexts = agent_answer.documents
            sample = SingleTurnSample(
                user_input=question,
                response=response,
                retrieved_contexts=retrieved_contexts,
                reference=reference,
            )
            samples.append(sample)
            time.sleep(1)

        self.logger.info(f"Validating {len(samples)} samples")
        eval_dataset = EvaluationDataset(samples=samples)

        if (
            self.azure_openai_api_key
            and self.azure_openai_endpoint
            and self.openai_api_version
            and self.azure_openai_deployment
            and self.azure_openai_embedding_deployment
        ):
            llm = AzureChatOpenAI(azure_deployment=self.azure_openai_deployment)
            embeddings = AzureOpenAIEmbeddings(
                azure_deployment=self.azure_openai_embedding_deployment,
                openai_api_version=self.openai_api_version,
            )
            evaluator_llm = LangchainLLMWrapper(llm)
            evaluator_embeddings = LangchainEmbeddingsWrapper(embeddings)
            self.logger.info("Configured RAGAS to work with Azure OpenAI.")
        else:
            self.logger.error("Azure OpenAI environment variables not set.")
            return

        metrics = [
            Faithfulness(llm=evaluator_llm),
            AnswerRelevancy(llm=evaluator_llm, embeddings=evaluator_embeddings),
            ContextPrecision(llm=evaluator_llm),
            ContextRecall(llm=evaluator_llm),
        ]

        self.logger.info("Evaluating metrics (may take a while)...")
        result = evaluate(dataset=eval_dataset, metrics=metrics)

        try:
            df = result.to_pandas()
            df.to_csv(report_path_csv, index=False)
            avg_metrics = {
                metric: df[metric].mean()
                for metric in [
                    "faithfulness",
                    "answer_relevancy",
                    "context_precision",
                    "context_recall",
                ]
            }
            template_path_obj = Path(report_template_path)
            if not template_path_obj.exists():
                self.logger.error(f"Template HTML not found: {template_path_obj}")
                return
            with open(template_path_obj, "r", encoding="utf-8") as f:
                template_content = f.read()
            for metric, value in avg_metrics.items():
                percentage = f"{value * 100:.2f}"
                template_content = template_content.replace(f"{{{{{metric}}}}}", percentage)
            with open(report_path_html, "w", encoding="utf-8") as f:
                f.write(template_content)
            self.logger.info(
                f"Ragas scan complete. Reports saved to:\n"
                f"- Detailed CSV report: {Path(report_path_csv).resolve()}\n"
                f"- Summary HTML report: {Path(report_path_html).resolve()}"
            )
        except Exception as e:
            self.logger.error(f"Error while saving the Ragas report: {e}")

    def run_all_scans(self) -> None:
        """
        Execute the two scans (simple and precision) and save the reports in the reports directory.
        """
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"Starting simple RAG scan, results will be saved in {self.reports_dir}")
        self.run_simple_scan()
        self.logger.info(f"Starting precision RAG scan, results will be saved in {self.reports_dir}")
        self.run_precision_scan()
        self.logger.info(f"Both RAG scans are complete. Reports are in {self.reports_dir.resolve()}")
