import logging
import os
import urllib3
import asyncio
from pathlib import Path
from dotenv import load_dotenv
from benchmark.generator import DataframeGenerator, TestsetGenerator
from benchmark.performance import ModelPerformance
from benchmark.security_scan import SecurityScanner
from benchmark.rag_scan import RAGScan
from benchmark.vlm_rag_scan import VisionRAGScan
from api.live_api import Models, Files

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Logging configuration
logging.basicConfig(
    level=logging.INFO, format="%(name)s - %(levelname)s - %(message)s", force=True
)
logger = logging.getLogger(__name__)


def main() -> None:
    logger.debug("Loading environment variables")
    load_dotenv()

    # Get all required environment variables
    JUDGE_MODEL = os.getenv("JUDGE_MODEL")
    JUDGE_EMBEDDING_MODEL = os.getenv("JUDGE_EMBEDDING_MODEL")
    LIVEINTELLIGENCE_PROD_API_KEY = os.getenv("PROD_KEY")
    LIVEINTELLIGENCE_PROD_ENDPOINT = os.getenv("PROD_URL")

    # -------------------------------------------------------------
    # Endpoint checks
    # -------------------------------------------------------------

    def check_endpoint_availability(prod_key, prod_url):
        """
        Check the availability of LiveIntelligence endpoints and return available models.

        Args:
            prod_key (str): API key for prod environment
            prod_url (str): Endpoint URL for prod environment

        Returns:
            tuple: Lists of available models for prod environment
        """
        available_models_prod = []

        logger.info("⌛ Checking LiveIntel API availability")
        available_models_prod = Models(prod_key, prod_url).execute()
        if available_models_prod:
            logger.info("✅ LiveIntel API available")
        else:
            logger.warning("⚠️ LiveIntel API not available !")

        return available_models_prod

    available_models_prod = check_endpoint_availability(
        LIVEINTELLIGENCE_PROD_API_KEY,
        LIVEINTELLIGENCE_PROD_ENDPOINT,
    )

    # -------------------------------------------------------------
    # Program flow functions
    # -------------------------------------------------------------

    def display_menu():
        logger.debug("Displaying interactive menu")
        print("\n==== Configuration ====")
        print(f"Selected model (via env) : {os.getenv('MODEL_TO_SCAN')}")
        print(
            f"Selected judge models and embedding models (via env) : {os.getenv('JUDGE_MODEL')} and {os.getenv('JUDGE_EMBEDDING_MODEL')}"
        )
        print("\n==== Live Intelligence Quality Scan ====")
        print("1. Build a question testset (required for RAG scan)")
        print("2. Launch a security scan")
        print("3. Launch a RAG scan (simple or precision)")
        print("4. Launch a VLM (Vision Language Model) RAG Scan")
        print("5. Get model information")
        print("Q. Quit")
        print("========================================")
        return input("Please select an option: ")

    def build_testset():
        logger.info("Launching question testset generation")

        # Ask the user for chunk size and overlap
        while True:
            chunk_size_input = input("Enter chunk size (default 1024): ").strip()
            if chunk_size_input == "":
                chunk_size = 1024
                break
            elif chunk_size_input.isdigit() and int(chunk_size_input) > 0:
                chunk_size = int(chunk_size_input)
                break
            else:
                print(
                    "Please enter a positive integer or leave blank for the default value."
                )

        while True:
            chunk_overlap_input = input("Enter overlap size (default 100): ").strip()
            if chunk_overlap_input == "":
                chunk_overlap = 100
                break
            elif chunk_overlap_input.isdigit() and int(chunk_overlap_input) >= 0:
                chunk_overlap = int(chunk_overlap_input)
                break
            else:
                print(
                    "Please enter a non-negative integer or leave blank for the default value."
                )

        # Instantiate the dataframe with the chosen values
        dataframe_builder = DataframeGenerator(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            knowledge_path="knowledge/",
        )
        dataframe = dataframe_builder.generate()

        # Then, generate a given number of questions (six different types) in a testset
        print(
            "You must specify the language of the knowledge base using the ISO 639-1 code."
        )
        print("For example, 'en' for English, 'fr' for French, 'es' for Spanish, etc.")
        knowledge_language = input(
            "Enter the language of the knowledge base (default 'en'): "
        ).strip()
        if knowledge_language == "":
            knowledge_language = "en"

        while True:
            total_questions_input = input(
                "Enter the number of questions to generate (minimum 6): "
            ).strip()
            if total_questions_input.isdigit():
                total_questions = int(total_questions_input)
                if total_questions >= 6:
                    break
                else:
                    print("Number of questions must be at least 6.")
            else:
                print("Invalid input. Please enter a valid number.")

        testset_generator = TestsetGenerator(
            dataframe,
            agent_description="An agent that can answer a wide scope of questions",
            llm_model=JUDGE_MODEL,
            embedding_model=JUDGE_EMBEDDING_MODEL,
            total_questions=total_questions,
            language=knowledge_language,
        )
        testset_generator.generate(Path("knowledge/testset.jsonl"))
        logger.info("Testset generated successfully and saved to testset.jsonl")

    def launch_security_scan():
        logger.info("Launching security scan")
        scanner = SecurityScanner()
        scanner.run()
        logger.info(
            "Security scan complete. Report saved to: reports/security_report.html"
        )

    def launch_rag_scan():
        logger.info("Launching a RAG scan")

        testset_path = Path("knowledge/testset.jsonl")
        docs_dir = Path("knowledge/")

        if not testset_path.exists():
            logger.error("RAG scan aborted: testset.jsonl not found.")
            return

        if not docs_dir.exists() or not any(docs_dir.iterdir()):
            logger.error(
                f"RAG scan aborted: Knowledge base directory '{docs_dir}' missing or empty."
            )
            return

        rag_scanner = RAGScan(logger=logger)

        print("\nRAG Scan Options:")
        print("1. Simple scan (general RAG modules quality)")
        print("2. Precision scan (precise text inspection)")
        print("3. Run both scans (reports will be saved in the 'reports' folder)")

        while True:
            choice = input("Select scan type (or 'b' to go back): ").strip().lower()
            if choice == "1":
                logger.debug("Selected simple RAG scan.")
                rag_scanner.run_simple_scan(
                    report_path="reports/scan_report_simple.html"
                )
                break
            elif choice == "2":
                logger.debug("Selected precision RAG scan.")
                rag_scanner.run_precision_scan(
                    report_path_csv="reports/scan_report_precision_ragas.csv",
                    report_path_html="reports/scan_report_precision_ragas.html",
                )
                break
            elif choice == "3":
                logger.debug("Selected both RAG scans.")
                rag_scanner.run_all_scans()
                break
            elif choice == "b":
                logger.debug("Returning to main menu")
                break
            else:
                print("Invalid option. Please try again.")

    def launch_vlm_rag_scan():
        logger.info("Launching a VLM RAG scan")
        print("\n==== VLM (Vision Language Model) RAG Scan ====")
        # Ask for the number of questions
        while True:
            n_questions_input = input(
                "Enter the number of questions to test (minimum 1, default 10): "
            ).strip()
            if n_questions_input == "":
                n_questions = 10
                break
            elif n_questions_input.isdigit() and int(n_questions_input) > 0:
                n_questions = int(n_questions_input)
                break
            else:
                print(
                    "Please enter a positive integer or leave blank for the default value."
                )

        # Ask for the pipeline to use
        print("\nChoose the tooling pipeline:")
        print("1. DocumentSearch (Complete)")
        print("2. VisionDocumentSearch (OCR Oriented)")
        while True:
            tool_choice = input("Your choice (1 or 2, default 2): ").strip()
            if tool_choice == "" or tool_choice == "2":
                tool = "VisionDocumentSearch"
                break
            elif tool_choice == "1":
                tool = "DocumentSearch"
                break
            else:
                print("Invalid option. Please choose 1 or 2.")

        # Workspace ID
        ws_id_input = input("Target workspace ID (default 1): ").strip()
        if ws_id_input == "" or not ws_id_input.isdigit():
            ws_id = 1
        else:
            ws_id = int(ws_id_input)

        # Get required environment variables
        api_key = os.getenv("PROD_KEY")
        base_url = os.getenv("PROD_URL")
        model = os.getenv("MODEL_TO_SCAN") or "alfred-4.1"
        images_dir = "knowledge/vlm_scan_data"
        output_csv = input(
            "Output CSV file name (default 'vlm_rag_results.csv'): "
        ).strip()
        if not output_csv:
            output_csv = "reports/vlm_rag_results.csv"

        # Instantiate the scanner
        scanner = VisionRAGScan(
            n_questions=n_questions,
            tool=tool,
            workspace_id=ws_id,
            api_key=api_key,
            base_url=base_url,
            model=model,
            images_dir=images_dir,
            log_level=logging.INFO,
        )

        # Download dataset and images if needed
        if not os.path.exists(f"{images_dir}/qa_data.json"):
            print("Downloading dataset and images...")
            scanner.download_dataset_and_images()
        else:
            print("Dataset already present, skipping download.")

        # Ask for image upload
        upload_choice = (
            input("Upload images to the target workspace? (Y/n, default Y): ")
            .strip()
            .lower()
        )
        if upload_choice == "" or upload_choice == "y":
            scanner.upload_images()
        else:
            print("Image upload skipped.")

        # Launch the main scan (async)
        print("Launching VLM scan, this may take several minutes...")
        try:
            async def _run_scan():
                await scanner.run_scan(csv_output_path=output_csv)

            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            loop.run_until_complete(_run_scan())
            print(f"Scan complete. Results exported to {output_csv}")
        except Exception as e:
            logger.error(f"Error during VLM scan: {e}")
            print(f"Error during VLM scan: {e}")

    def launch_model_info():
        logger.info("Launching model information")

        print("Performance options:")
        print("1. Test max input tokens")
        print("2. Test streaming chunk timing (ChatCompletions)")
        print("3. Test RAG chain response time")

        while True:
            choice = input("Select an option: ").strip().lower()
            if choice == "1":
                logger.info("Testing model max tokens")
                perf_obj = ModelPerformance(os.getenv("MODEL_TO_SCAN"))
                max_tokens, time_summary = perf_obj.test_max_context()
                logger.info(f"✅ Maximum limit found: {max_tokens} characters")
                if time_summary is not None:
                    logger.info(f"🔍 Time to completion summary:")
                    low, medium, high = time_summary
                    logger.info(f"Low:    {low[0]} chars, {low[1]:.3f} sec")
                    logger.info(f"Medium: {medium[0]} chars, {medium[1]:.3f} sec")
                    logger.info(f"High:   {high[0]} chars, {high[1]:.3f} sec")
                break
            elif choice == "2":
                logger.info("Testing streaming chunk timing (ChatCompletions)")
                perf_obj = ModelPerformance(os.getenv("MODEL_TO_SCAN"))
                prompt = input(
                    "🔍 Enter prompt to test (or leave empty for default prompt): "
                ).strip()
                if not prompt:
                    prompt = None
                result = perf_obj.test_streaming_performance(prompt=prompt)
                if not result:
                    logger.error("Error during streaming performance audit.")
                    break

                usage = result["usage"] or {}
                prompt_tokens = usage.get("prompt_tokens", "N/A")
                completion_tokens = usage.get("completion_tokens", "N/A")
                total_tokens = usage.get("total_tokens", "N/A")

                logger.info("🔤 TOKEN STATISTICS")
                logger.info(f"├─ Prompt tokens    : {prompt_tokens}")
                logger.info(f"├─ Generated tokens : {completion_tokens}")
                logger.info(f"└─ Total tokens     : {total_tokens}")

                if result["chunk_times"]:
                    logger.info("⏱️  TIMING ANALYSIS")
                    first_token_latency = result.get("first_token_latency")
                    if first_token_latency is not None:
                        logger.info(
                            f"├─ Time before first token: {first_token_latency:.3f} sec"
                        )
                    logger.info(
                        f"├─ Total generation time  : {result['total_time']:.3f} sec"
                    )
                    average = (
                        result["total_time"] / len(result["chunk_times"])
                        if result["chunk_times"]
                        else 0
                    )
                    logger.info(f"└─ Avg time between chunks: {average:.3f} sec")
                else:
                    logger.info("⚠️  No chunks received or only one chunk.")

                break
            elif choice == "3":
                logger.info("Testing RAG chain response time")
                perf_obj = ModelPerformance(os.getenv("MODEL_TO_SCAN"))

                # Get workspace ID
                ws_id_input = input("🔍 Enter workspace ID (default 1): ").strip()
                if ws_id_input == "" or not ws_id_input.isdigit():
                    ws_id = 1
                else:
                    ws_id = int(ws_id_input)

                # Get scope options
                company_scope_input = (
                    input("🏢 Include company documents? (y/N): ").strip().lower()
                )
                company_scope = company_scope_input == "y"
                private_scope_input = (
                    input("🔒 Include private documents? (y/N): ").strip().lower()
                )
                private_scope = private_scope_input == "y"

                # Get document count
                api_key = os.getenv("PROD_KEY")
                base_url = os.getenv("PROD_URL")
                files_api = Files(api_key, base_url, perf_obj.model)

                logger.info("📚 DOCUMENT SCOPE")
                try:
                    files = files_api.execute(
                        workspace_scope=ws_id,
                        company_scope=company_scope,
                        private_scope=private_scope,
                    )
                    logger.info(f"├─ Workspace ID      : {ws_id}")
                    logger.info(
                        f"├─ Company scope     : {'Yes' if company_scope else 'No'}"
                    )
                    logger.info(
                        f"├─ Private scope     : {'Yes' if private_scope else 'No'}"
                    )
                    logger.info(f"└─ Documents found   : {len(files)}")
                except Exception as e:
                    logger.error(f"Error fetching files for workspace {ws_id}: {e}")
                    break

                # Get prompt and output path
                prompt = input(
                    "🔍 Enter prompt to test (or leave empty for default): "
                ).strip()
                if not prompt:
                    prompt = "Test prompt for DocumentSearch endpoint."

                output_path = input(
                    "📄 Output file name (default 'document_search_response'): "
                ).strip()
                if not output_path:
                    output_path = "document_search_response.json"

                # Run test
                result = perf_obj.test_document_search_performance(
                    workspace_id=ws_id,
                    prompt=prompt,
                    output_path=output_path,
                    company_scope=company_scope,
                    private_scope=private_scope,
                )

                if not result:
                    logger.error("Error during DocumentSearch test.")
                    break

                logger.info("⏱️  TIMING ANALYSIS")
                logger.info(f"└─ Total search time: {result['total_time']:.3f} sec")
                logger.info(f"💾 Raw response saved in {output_path}")
                break
            else:
                logger.error("Invalid option. Please try again.")

    # -------------------------------------------------------------
    # Main menu loop
    # -------------------------------------------------------------

    logger.debug("Starting interactive menu")
    while True:
        choice = display_menu().strip().lower()

        if choice == "1":
            build_testset()
        elif choice == "2":
            launch_security_scan()
        elif choice == "3":
            launch_rag_scan()
        elif choice == "4":
            launch_vlm_rag_scan()
        elif choice == "5":
            launch_model_info()
        elif choice == "q":
            logger.debug("Exiting program")
            print("Closing program")
            break
        else:
            print("Invalid option. Please try again.")


if __name__ == "__main__":
    main()
