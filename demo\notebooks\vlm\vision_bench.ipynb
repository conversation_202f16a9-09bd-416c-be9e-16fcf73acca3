{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"This notebook is used to benchmark the VLM models. It uses a image-exclusive dataset.\n", "Exactness is measured in 3 sync and async steps :\n", "- 10 questions\n", "- 100 questions\n", "- 1000 questions\n", "6 tests are performed and charted to show the performance of the model.\n", "Async mode is used to observe the performance along the exactness and stress test the API.\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Required imports and dataset loading"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import dspy\n", "import logging\n", "from typing import List, Dict, Any, Tuple, Optional\n", "from openai import AzureOpenAI\n", "import time\n", "import sys\n", "sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '../../..')))\n", "from datasets import load_dataset\n", "import glob\n", "from src.api.live_api import FileUploader, DocumentSearch, Files\n", "import tqdm\n", "import json\n", "import zlib\n", "from tqdm.asyncio import tqdm as async_tqdm\n", "from io import BytesIO\n", "from src.api.live_api import AsyncDocumentSearch, Files\n", "from PIL import Image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds = load_dataset(\"Ryoo72/InfographicsVQA\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download all images, then prepare a JSON with their questions and answers.\n", "There is multiple questions per image, below we are ensuring to have all unique images and their related questions and answers.\n", "The same image can not be downloaded twice, we are using a CRC32 to check for duplicates."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.makedirs(\"images\", exist_ok=True)\n", "Image.MAX_IMAGE_PIXELS = None\n", "\n", "all_samples = ds[\"test\"]  # Get all samples instead of just 1000\n", "image_crcs = {}  # CRC32 -> idx (to avoid duplicates)\n", "idx_to_imagefile = {}  # idx of sample -> image file name and CRC\n", "\n", "# Save unique images and mapping\n", "for idx, sample in tqdm.tqdm(enumerate(all_samples), total=len(all_samples), desc=f\"Processing {len(all_samples)} rows from the dataset\"):\n", "    img = sample['image']\n", "    img_byte_arr = BytesIO()\n", "    img.save(img_byte_arr, format=img.format)\n", "    img_byte_arr = img_byte_arr.getvalue()\n", "    crc = zlib.crc32(img_byte_arr)\n", "    if crc in image_crcs:\n", "        # Image already seen, reference existing image\n", "        idx_to_imagefile[idx] = {\n", "            \"image_file\": f\"image_{image_crcs[crc]}.jpg\",\n", "            \"crc\": crc\n", "        }\n", "        continue\n", "    # New image, save it\n", "    image_crcs[crc] = idx\n", "    img.save(f\"images/image_{idx}.jpg\")\n", "    idx_to_imagefile[idx] = {\n", "        \"image_file\": f\"image_{idx}.jpg\",\n", "        \"crc\": crc\n", "    }\n", "\n", "print(f\"{len(image_crcs)} images uniques téléchargées et sauvées dans le dossier 'images'\")\n", "\n", "# Preparation of JSON question/answer related to each image\n", "qa_data_list = []\n", "for idx, sample in tqdm.tqdm(enumerate(all_samples), total=len(all_samples), desc=f\"Processing {len(all_samples)} questions and answers\"):\n", "    questions = sample['question']\n", "    answers = sample['answers']\n", "    image_info = idx_to_imagefile[idx]\n", "    # If multiple questions, process all of them\n", "    if isinstance(questions, list):\n", "        for q, a in zip(questions, answers):\n", "            qa_data_list.append({\n", "                \"image_file\": image_info[\"image_file\"],\n", "                \"crc\": image_info[\"crc\"],\n", "                \"question\": q,\n", "                \"answers\": a\n", "            })\n", "    else:\n", "        qa_data_list.append({\n", "            \"image_file\": image_info[\"image_file\"],\n", "            \"crc\": image_info[\"crc\"],\n", "            \"question\": questions,\n", "            \"answers\": answers\n", "        })\n", "\n", "# Save JSON\n", "with open(\"images/qa_data.json\", \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(qa_data_list, f, indent=4, ensure_ascii=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define and prepare a DSPy LLM Judge for the other batches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare the Judge with DS<PERSON>y, it will answer with a Yes or a No if the provided answer by Trust is valid\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(\"VLM-Judge\")\n", "\n", "# Get Azure OpenAI configuration from environment variables\n", "env_azure_api_key = os.getenv(\"AZURE_API_KEY\")\n", "env_azure_base_endpoint = os.getenv(\"AZURE_API_BASE\")\n", "env_azure_version = os.getenv(\"AZURE_API_VERSION\")\n", "\n", "# Create Azure OpenAI client for DSPy\n", "azure_client = AzureOpenAI(\n", "    api_key=env_azure_api_key,\n", "    api_version=env_azure_version,\n", "    azure_endpoint=env_azure_base_endpoint\n", ")\n", "\n", "# Define the DSPy module for the judge\n", "class JudgeSignature(dspy.Signature):\n", "    \"\"\"Judge if a VLM's answer is correct based on expected answers.\"\"\"\n", "    question = dspy.InputField(desc=\"The question asked to the VLM\")\n", "    vlm_answer = dspy.InputField(desc=\"The answer provided by the VLM\")\n", "    expected_answers = dspy.InputField(desc=\"List of acceptable answers from the dataset\")\n", "    judgment = dspy.OutputField(desc=\"'YES' if the answer is correct, 'NO' otherwise\")\n", "\n", "class VLMJudge(dspy.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.judge = dspy.ChainOfThought(JudgeSignature)\n", "        \n", "    def forward(self, question: str, vlm_answer: str, expected_answers: List[str]) -> str:\n", "        \"\"\"\n", "        Judge if the VLM's answer is correct based on the expected answers.\n", "        \n", "        Args:\n", "            question: The question asked to the VLM\n", "            vlm_answer: The answer provided by the VLM\n", "            expected_answers: List of acceptable answers from the dataset\n", "            \n", "        Returns:\n", "            \"YES\" if the answer is correct, \"NO\" otherwise\n", "        \"\"\"\n", "        try:\n", "            # Format expected answers as a string for better readability\n", "            expected_str = \", \".join(expected_answers)\n", "            \n", "            # Use DSPy's structured approach to get the judgment\n", "            response = self.judge(\n", "                question=question,\n", "                vlm_answer=vlm_answer,\n", "                expected_answers=expected_str\n", "            )\n", "            \n", "            # Extract and normalize the judgment\n", "            judgment = response.judgment.strip().upper()\n", "            \n", "            # Ensure we only return YES or NO\n", "            if judgment == \"YES\" or judgment == \"NO\":\n", "                return judgment\n", "            else:\n", "                logger.warning(f\"Judge returned invalid response: {judgment}. Defaulting to NO.\")\n", "                return \"NO\"\n", "        except Exception as e:\n", "            logger.error(f\"Error in judge evaluation: {str(e)}\")\n", "            return \"NO\"  # Default to NO on error\n", "\n", "# Configure DSPy with Azure OpenAI\n", "try:\n", "    # Set up the Azure OpenAI model for DSPy\n", "    # For Azure OpenAI, we use the format \"openai/deployment-name\" and provide the Azure-specific parameters\n", "    azure_lm = dspy.LM(\n", "        model=\"azure/gpt-4.1-liveintel\",  # Use the deployment name from your Azure account\n", "        api_key=env_azure_api_key,\n", "        api_base=env_azure_base_endpoint,\n", "        api_version=env_azure_version\n", "    )\n", "    \n", "    # Configure DSPy to use the Azure OpenAI model\n", "    dspy.configure(lm=azure_lm)\n", "    \n", "    logger.info(\"DSPy configured successfully with Azure OpenAI\")\n", "except Exception as e:\n", "    logger.error(f\"Failed to configure DSPy with Azure OpenAI: {str(e)}\")\n", "\n", "# Create a batch-optimized judge function\n", "def batch_judge(questions: List[str], vlm_answers: List[str], expected_answers: List[List[str]]) -> List[str]:\n", "    \"\"\"\n", "    Process multiple question-answer pairs in batch.\n", "    \n", "    Args:\n", "        questions: List of questions\n", "        vlm_answers: List of answers from the VLM\n", "        expected_answers: List of lists of acceptable answers\n", "        \n", "    Returns:\n", "        List of judgments (\"YES\" or \"NO\")\n", "    \"\"\"\n", "    if not (len(questions) == len(vlm_answers) == len(expected_answers)):\n", "        logger.error(f\"Mismatched input lengths: {len(questions)} questions, {len(vlm_answers)} VLM answers, {len(expected_answers)} expected answers\")\n", "        raise ValueError(\"Input lists must have the same length\")\n", "    \n", "    judge = VLMJudge()\n", "    results = []\n", "    \n", "    for i, (question, vlm_answer, expected) in enumerate(zip(questions, vlm_answers, expected_answers)):\n", "        try:\n", "            logger.info(f\"Judging question {i+1}/{len(questions)}\")\n", "            result = judge(question, vlm_answer, expected)\n", "            results.append(result)\n", "        except Exception as e:\n", "            logger.error(f\"Error judging question {i+1}: {str(e)}\")\n", "            results.append(\"NO\")  # Default to NO on error\n", "    \n", "    return results\n", "\n", "# Example usage:\n", "# judgments = batch_judge(\n", "#     questions=[\"What color is the sky?\", \"What is 2+2?\"],\n", "#     vlm_answers=[\"<PERSON>\", \"Four\"],\n", "#     expected_answers=[[\"Blue\", \"Azure\"], [\"4\", \"Four\"]]\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Upload the files to our personal space using batch processing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Define API key and base URL*"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["API_KEY = \"toKTSqr7.juIuOPGsW4V0AvSEon0KnQD3b76iNK0E\"\n", "BASE_URL = \"https://trust.pprd.liveintelligence.orange-business.com/\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Launch the upload*"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["uploader = FileUploader(api_key=API_KEY, base_url=BASE_URL)\n", "\n", "# Deactivate any existing sessions before starting\n", "try:\n", "    answ = uploader.deactivate_all_sessions()\n", "    print(f\"Tentative de réinitialisation des sessions existantes : {answ}\")\n", "except Exception as e:\n", "    print(f\"Erreur lors de la réinitialisation des sessions : {e}\")\n", "\n", "# Get list of image files to upload\n", "image_files = [f for f in glob.glob(\"images/*\") if f.lower().endswith(('.jpg', '.jpeg', '.png'))]\n", "\n", "# Upload files using the new batch processing function\n", "# This implementation follows the established pattern:\n", "# - Process files in batches of maximum 10 files\n", "# - Use get_session_details() to monitor processing status\n", "# - Wait for all documents in a batch to complete processing before starting the next batch\n", "# - Properly clean up sessions between batches\n", "print(f\"Starting batch upload of {len(image_files)} images...\")\n", "\n", "try:\n", "    upload_result = uploader.upload_files_in_batches(\n", "        file_paths=image_files,\n", "        batch_size=30,  # Maximum 10 files per batch as per established pattern\n", "        max_wait_time=3600,  # 1 hour timeout per batch\n", "        check_interval=30,  # Check status every 10 seconds\n", "        collection_type=\"workspace\",\n", "        workspace_id=19\n", "    )\n", "    \n", "    # Affichage des résultats d'upload\n", "    print(f\"\\n=== Résumé Upload ===\")\n", "    print(f\"Statut: {upload_result['status']}\")\n", "    print(f\"Fichiers totaux: {upload_result['total_files']}\")\n", "    print(f\"Uploads ré<PERSON><PERSON>: {upload_result['successful_uploads']}\")\n", "    print(f\"Uploads échoués: {upload_result['failed_uploads']}\")\n", "    print(f\"Lots traités: {upload_result['batches_processed']}\")\n", "    \n", "    # Détails des uploads échoués\n", "    if upload_result['failed_uploads'] > 0:\n", "        print(f\"\\n=== Détails Uploads Échoués ===\")\n", "        for batch_detail in upload_result['details']:\n", "            if batch_detail['failed_files']:\n", "                print(f\"Lot {batch_detail['batch_number']}:\")\n", "                for failed_file in batch_detail['failed_files']:\n", "                    print(f\"  - {failed_file['file_path']}: {failed_file['error']}\")\n", "    \n", "except Exception as e:\n", "    print(f\"Erreur lors de l'upload par lots : {e}\")\n", "    raise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BATCH 1 - 1 image per multiple questions - 50 questions - Alfred 4.1 - Manual Review"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Workspace \"Giskard\" sur la préprod\n", "WORKSPACE_ID = 19\n", "\n", "# Charger les questions/réponses\n", "with open(\"images/qa_data.json\", \"r\", encoding=\"utf-8\") as f:\n", "    qa_data = json.load(f)\n", "\n", "# Regrouper les questions par image\n", "image_to_questions = {}\n", "for item in qa_data:\n", "    img_file = item[\"image_file\"]\n", "    if img_file not in image_to_questions:\n", "        image_to_questions[img_file] = []\n", "    image_to_questions[img_file].append({\n", "        \"question\": item[\"question\"],\n", "        \"answers\": item[\"answers\"]\n", "    })\n", "\n", "# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID\n", "files_api = Files(api_key=API_KEY, base_url=BASE_URL)\n", "uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)\n", "print(uploaded_files)\n", "\n", "# Correction ici : on mappe les .jpg locaux aux .pdf côté API\n", "filename_to_id = {}\n", "for f in uploaded_files:\n", "    if f[\"filename\"].startswith(\"image_\") and f[\"filename\"].endswith(\".pdf\"):\n", "        # On remplace .pdf par .jpg pour matcher les noms locaux\n", "        local_name = f[\"filename\"].replace(\".pdf\", \".jpg\")\n", "        filename_to_id[local_name] = f[\"id\"]\n", "\n", "# Initialiser l'API de recherche\n", "doc_search = DocumentSearch(api_key=API_KEY, base_url=BASE_URL)\n", "\n", "results = []\n", "response_times = []\n", "start_time = time.time()\n", "\n", "for img_file, qas in tqdm.tqdm(image_to_questions.items(), desc=\"Interrogation des images\"):\n", "    file_id = filename_to_id.get(img_file)\n", "    if not file_id:\n", "        print(f\"Image {img_file} introuvable côté API, ignorée.\")\n", "        continue\n", "\n", "    # Pour chaque question associée à cette image\n", "    for qa in qas:\n", "        question = qa[\"question\"]\n", "        try:\n", "            request_start = time.time()\n", "            response = doc_search.execute(\n", "                query=question,\n", "                workspace_ids=[WORKSPACE_ID],\n", "                file_ids=[file_id],  # On passe l'ID du fichier image\n", "                tool=\"VisionDocumentSearch\"\n", "            )\n", "            request_time = time.time() - request_start\n", "            response_times.append(request_time)\n", "        except Exception as e:\n", "            response = {\"error\": str(e)}\n", "            request_time = -1\n", "            response_times.append(request_time)\n", "\n", "        results.append({\n", "            \"image_file\": img_file,\n", "            \"file_id\": file_id,\n", "            \"question\": question,\n", "            \"answers\": qa[\"answers\"],\n", "            \"api_response\": response,\n", "            \"response_time\": request_time\n", "        })\n", "\n", "total_time = time.time() - start_time\n", "avg_response_time = sum([t for t in response_times if t > 0]) / len([t for t in response_times if t > 0])\n", "\n", "# Ajouter les métadonnées de temps\n", "metadata = {\n", "    \"response_times\": response_times,\n", "    \"average_response_time\": avg_response_time,\n", "    \"total_process_time\": total_time,\n", "    \"total_requests\": len(response_times),\n", "    \"failed_requests\": len([t for t in response_times if t < 0])\n", "}\n", "\n", "final_output = {\n", "    \"results\": results,\n", "    \"metadata\": metadata\n", "}\n", "\n", "# Sauvegarde des résultats\n", "with open(\"images/vision_results.json\", \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(final_output, f, indent=2, ensure_ascii=False)\n", "\n", "print(\"Résultats sauvés dans images/vision_results.json\")\n", "print(f\"\\nStatistiques :\")\n", "print(f\"Temps total : {total_time:.2f}s\")\n", "print(f\"Temps moyen/requête : {avg_response_time:.2f}s\")\n", "print(f\"Total requêtes : {len(response_times)}\")\n", "print(f\"Requêtes échouées : {len([t for t in response_times if t < 0])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BATCH 2 - 1 image per multiple questions - 100 questions - Alfred 4.1 - Auto Review by a GPT 4.1 DSPy Judge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Workspace \"Giskard\" sur la préprod\n", "WORKSPACE_ID = 19\n", "\n", "# Charger les questions/réponses\n", "with open(\"images/qa_data.json\", \"r\", encoding=\"utf-8\") as f:\n", "    qa_data = json.load(f)\n", "\n", "# Regrouper les questions par image\n", "image_to_questions = {}\n", "for item in qa_data:\n", "    img_file = item[\"image_file\"]\n", "    if img_file not in image_to_questions:\n", "        image_to_questions[img_file] = []\n", "    image_to_questions[img_file].append({\n", "        \"question\": item[\"question\"],\n", "        \"answers\": item[\"answers\"]\n", "    })\n", "\n", "# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID\n", "files_api = Files(api_key=API_KEY, base_url=BASE_URL)\n", "uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)\n", "print(uploaded_files)\n", "\n", "# Correction ici : on mappe les .jpg locaux aux .pdf côté API\n", "filename_to_id = {}\n", "for f in uploaded_files:\n", "    if f[\"filename\"].startswith(\"image_\") and f[\"filename\"].endswith(\".pdf\"):\n", "        # On remplace .pdf par .jpg pour matcher les noms locaux\n", "        local_name = f[\"filename\"].replace(\".pdf\", \".jpg\")\n", "        filename_to_id[local_name] = f[\"id\"]\n", "\n", "# Initialiser l'API de recherche\n", "doc_search = DocumentSearch(api_key=API_KEY, base_url=BASE_URL)\n", "\n", "results = []\n", "\n", "# Listes pour le traitement par lots avec le juge DSPy\n", "all_questions = []\n", "all_vlm_answers = []\n", "all_expected_answers = []\n", "\n", "for img_file, qas in tqdm.tqdm(image_to_questions.items(), desc=\"Interrogation des images ...\"):\n", "    file_id = filename_to_id.get(img_file)\n", "    if not file_id:\n", "        print(f\"Image {img_file} introuvable côté API, ignorée.\")\n", "        continue\n", "\n", "    # Pour chaque question associée à cette image\n", "    for qa in qas:\n", "        question = qa[\"question\"]\n", "        try:\n", "            response = doc_search.execute(\n", "                query=question,\n", "                workspace_ids=[WORKSPACE_ID],\n", "                file_ids=[file_id],  # On passe l'ID du fichier image\n", "                tool=\"VisionDocumentSearch\",\n", "                model=\"alfred-4.1\"\n", "            )\n", "            \n", "            # Extraire la réponse du VLM\n", "            vlm_answer = response.get(\"answer\", \"\")\n", "            if not vlm_answer and \"error\" not in response:\n", "                # Essayer d'extraire la réponse d'une autre façon si le format est différent\n", "                vlm_answer = response.get(\"content\", \"\")\n", "                if not vlm_answer:\n", "                    # Dernier recours: convertir toute la réponse en chaîne\n", "                    vlm_answer = str(response)\n", "            \n", "        except Exception as e:\n", "            response = {\"error\": str(e)}\n", "            # Pour les erreurs, utiliser une réponse par défaut\n", "            vlm_answer = \"ERROR: Unable to process question\"\n", "        \n", "        # Toujours ajouter aux listes pour le traitement par lots (même en cas d'erreur)\n", "        all_questions.append(question)\n", "        all_vlm_answers.append(vlm_answer)\n", "        all_expected_answers.append(qa[\"answers\"] if isinstance(qa[\"answers\"], list) else [qa[\"answers\"]])\n", "\n", "        results.append({\n", "            \"image_file\": img_file,\n", "            \"file_id\": file_id,\n", "            \"question\": question,\n", "            \"answers\": qa[\"answers\"],\n", "            \"api_response\": response\n", "        })\n", "\n", "# Vérification du nombre de questions traitées\n", "print(f\"\\nQuestions traitées batch 2: {len(all_questions)}\")\n", "print(f\"Résultats totaux: {len(results)}\")\n", "\n", "# Utiliser le juge DSPy pour évaluer les réponses en lot\n", "print(f\"\\nÉvaluation de {len(all_questions)} réponses avec juge DSPy...\")\n", "try:\n", "    judgments = batch_judge(all_questions, all_vlm_answers, all_expected_answers)\n", "    \n", "    # Ajouter les jugements aux résultats\n", "    judgment_index = 0\n", "    for i, result in enumerate(results):\n", "        if \"error\" not in result[\"api_response\"]:\n", "            if judgment_index < len(judgments):\n", "                result[\"judge_verdict\"] = judgments[judgment_index]\n", "                judgment_index += 1\n", "            else:\n", "                result[\"judge_verdict\"] = \"NO_JUDGMENT\"\n", "        else:\n", "            result[\"judge_verdict\"] = \"ERROR\"\n", "    \n", "    # Calculer les statistiques\n", "    correct_answers = judgments.count(\"YES\")\n", "    total_judged = len(judgments)\n", "    accuracy = (correct_answers / total_judged) * 100 if total_judged > 0 else 0\n", "    \n", "    print(f\"\\nRésultats évaluation:\")\n", "    print(f\"Réponses correctes: {correct_answers}/{total_judged} ({accuracy:.2f}%)\")\n", "    \n", "except Exception as e:\n", "    print(f\"Erreur lors de l'évaluation par le juge DSPy: {str(e)}\")\n", "\n", "# Sauvegarde des résultats\n", "with open(\"images/results_batch_2.json\", \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(results, f, indent=2, ensure_ascii=False)\n", "\n", "print(\"\\nRésultats sauvés dans images/results_batch_2.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BATCH 3 & 4 - All images in scope - 100 & 1000 questions - Alfred 4.1 - Auto Review by a GPT 4.1 DSPy Judge - Tool DocumentSearch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Workspace \"Giskard\" sur la préprod\n", "WORKSPACE_ID = 19\n", "\n", "# Charger les questions/réponses\n", "with open(\"images/qa_data.json\", \"r\", encoding=\"utf-8\") as f:\n", "    qa_data = json.load(f)\n", "\n", "# Limiter à X premières lignes du fichier data\n", "# qa_data = qa_data[:100]\n", "\n", "# Regrouper les questions par image\n", "image_to_questions = {}\n", "for item in qa_data:\n", "    img_file = item[\"image_file\"]\n", "    if img_file not in image_to_questions:\n", "        image_to_questions[img_file] = []\n", "    image_to_questions[img_file].append({\n", "        \"question\": item[\"question\"],\n", "        \"answers\": item[\"answers\"]\n", "    })\n", "\n", "# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID\n", "files_api = Files(api_key=API_KEY, base_url=BASE_URL)\n", "uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)\n", "print(uploaded_files)\n", "\n", "# Correction ici : on mappe les .jpg locaux aux .pdf côté API\n", "filename_to_id = {}\n", "for f in uploaded_files:\n", "    if f[\"filename\"].startswith(\"image_\") and f[\"filename\"].endswith(\".pdf\"):\n", "        # On remplace .pdf par .jpg pour matcher les noms locaux\n", "        local_name = f[\"filename\"].replace(\".pdf\", \".jpg\")\n", "        filename_to_id[local_name] = f[\"id\"]\n", "\n", "# Initialiser l'API de recherche\n", "doc_search = DocumentSearch(api_key=API_KEY, base_url=BASE_URL)\n", "\n", "results_batch3 = []\n", "all_questions_batch3 = []\n", "all_vlm_answers_batch3 = []\n", "all_expected_answers_batch3 = []\n", "response_times_batch3 = []\n", "\n", "start_time = time.time()\n", "\n", "for img_file, qas in tqdm.tqdm(image_to_questions.items(), desc=\"Interrogation globale des images ...\"):\n", "    for qa in qas:\n", "        question = qa[\"question\"]\n", "        try:\n", "            request_start = time.time()\n", "            response = doc_search.execute(\n", "                query=question,\n", "                workspace_ids=[WORKSPACE_ID],\n", "                tool=\"DocumentSearch\",  # Utilisation du nouveau tool\n", "                model=\"alfred-4.1\"\n", "            )\n", "            request_time = time.time() - request_start\n", "            response_times_batch3.append(request_time)\n", "\n", "            # Extraire la réponse du VLM\n", "            vlm_answer = response.get(\"answer\", \"\")\n", "            if not vlm_answer and \"error\" not in response:\n", "                vlm_answer = response.get(\"content\", \"\")\n", "                if not vlm_answer:\n", "                    vlm_answer = str(response)\n", "\n", "        except Exception as e:\n", "            response = {\"error\": str(e)}\n", "            request_time = -1\n", "            response_times_batch3.append(request_time)\n", "            vlm_answer = \"ERROR: Unable to process question\"\n", "\n", "        # Toujours ajouter aux listes pour le traitement par lots (même en cas d'erreur)\n", "        all_questions_batch3.append(question)\n", "        all_vlm_answers_batch3.append(vlm_answer)\n", "        all_expected_answers_batch3.append(qa[\"answers\"] if isinstance(qa[\"answers\"], list) else [qa[\"answers\"]])\n", "\n", "        # Récupérer l'ID de l'image originale pour référence\n", "        original_file_id = filename_to_id.get(img_file)\n", "\n", "        # Extraire les noms des documents trouvés par le système (ex: \"image_444\")\n", "        found_image_names = []\n", "        if \"documents\" in response:\n", "            for doc in response.get(\"documents\", []):\n", "                # On récupère le nom du document (sans extension)\n", "                name = doc.get(\"name\", \"\")\n", "                found_image_names.append(name)\n", "\n", "        results_batch3.append({\n", "            \"image_file\": img_file,\n", "            \"original_file_id\": original_file_id,\n", "            \"found_image_names\": found_image_names,\n", "            \"question\": question,\n", "            \"answers\": qa[\"answers\"],\n", "            \"api_response\": response,\n", "            \"response_time\": request_time\n", "        })\n", "\n", "total_time = time.time() - start_time\n", "avg_response_time = sum([t for t in response_times_batch3 if t > 0]) / len([t for t in response_times_batch3 if t > 0]) if len([t for t in response_times_batch3 if t > 0]) > 0 else 0\n", "\n", "# Vérification du nombre de questions traitées\n", "print(f\"\\nQuestions traitées batch 3: {len(all_questions_batch3)}\")\n", "print(f\"Résultats totaux: {len(results_batch3)}\")\n", "\n", "# Utiliser le juge DSPy pour évaluer les réponses en lot\n", "print(f\"\\nÉvaluation de {len(all_questions_batch3)} réponses avec juge DSPy...\")\n", "try:\n", "    judgments_batch3 = batch_judge(all_questions_batch3, all_vlm_answers_batch3, all_expected_answers_batch3)\n", "\n", "    # Ajouter les jugements aux résultats\n", "    judgment_index = 0\n", "    for i, result in enumerate(results_batch3):\n", "        if \"error\" not in result[\"api_response\"]:\n", "            if judgment_index < len(judgments_batch3):\n", "                result[\"judge_verdict\"] = judgments_batch3[judgment_index]\n", "                judgment_index += 1\n", "            else:\n", "                result[\"judge_verdict\"] = \"NO_JUDGMENT\"\n", "        else:\n", "            result[\"judge_verdict\"] = \"ERROR\"\n", "\n", "    # Calculer les statistiques\n", "    correct_answers = judgments_batch3.count(\"YES\")\n", "    total_judged = len(judgments_batch3)\n", "    accuracy = (correct_answers / total_judged) * 100 if total_judged > 0 else 0\n", "\n", "    # Calculer le taux de réussite pour trouver la bonne image\n", "    correct_image_found = 0\n", "    for result in results_batch3:\n", "        # On considère que le document cible est trouvé si le nom de l'image (sans extension) est dans found_image_names\n", "        target_name = result[\"image_file\"].replace(\".jpg\", \"\").replace(\".jpeg\", \"\").replace(\".png\", \"\")\n", "        if target_name in result[\"found_image_names\"]:\n", "            correct_image_found += 1\n", "\n", "    image_accuracy = (correct_image_found / len(results_batch3)) * 100 if len(results_batch3) > 0 else 0\n", "\n", "    # Affichage des statistiques principales\n", "    print(f\"\\nRésultats évaluation batch 3:\")\n", "    print(f\"Réponses correctes (DSPy Judge): {correct_answers}/{total_judged} ({accuracy:.2f}%)\")\n", "    print(f\"Bon document trouvé: {correct_image_found}/{len(results_batch3)} ({image_accuracy:.2f}%)\")\n", "    print(f\"Temps moyen/requête: {avg_response_time:.2f}s\")\n", "\n", "except Exception as e:\n", "    print(f\"Erreur lors de l'évaluation par le juge DSPy: {str(e)}\")\n", "\n", "# Sauvegarde des résultats\n", "final_output = {\n", "    \"results\": results_batch3,\n", "    \"metadata\": {\n", "        \"average_response_time\": avg_response_time,\n", "        \"total_process_time\": total_time,\n", "        \"total_requests\": len(response_times_batch3),\n", "        \"answer_accuracy\": accuracy if 'accuracy' in locals() else None,\n", "        \"image_search_accuracy\": image_accuracy if 'image_accuracy' in locals() else None\n", "    }\n", "}\n", "\n", "with open(\"images/results_batch_3.json\", \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(final_output, f, indent=2, ensure_ascii=False)\n", "\n", "print(\"\\nRésultats sauvés dans images/results_batch_3.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### BATCH 5 - All images in scope - 3000 ASYNC questions - Alfred 4.1 - Auto Review by a GPT 4.1 DSPy Judge - Tool DocumentSearch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Workspace \"Giskard\" sur la préprod\n", "WORKSPACE_ID = 19\n", "\n", "# Charger les questions/réponses\n", "with open(\"images/qa_data.json\", \"r\", encoding=\"utf-8\") as f:\n", "    qa_data = json.load(f)\n", "\n", "# Regrouper les questions par image\n", "image_to_questions = {}\n", "for item in qa_data:\n", "    img_file = item[\"image_file\"]\n", "    if img_file not in image_to_questions:\n", "        image_to_questions[img_file] = []\n", "    image_to_questions[img_file].append({\n", "        \"question\": item[\"question\"],\n", "        \"answers\": item[\"answers\"]\n", "    })\n", "\n", "# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID\n", "files_api = Files(api_key=API_KEY, base_url=BASE_URL)\n", "uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)\n", "filename_to_id = {}\n", "for f in uploaded_files:\n", "    if f[\"filename\"].startswith(\"image_\") and f[\"filename\"].endswith(\".pdf\"):\n", "        local_name = f[\"filename\"].replace(\".pdf\", \".jpg\")\n", "        filename_to_id[local_name] = f[\"id\"]\n", "\n", "# P<PERSON><PERSON>er les requêtes asynchrones avec le nom du fichier cible\n", "async_requests = []\n", "target_image_names = []  # Pour le calcul du taux de bons documents trouvés\n", "for img_file, qas in image_to_questions.items():\n", "    for qa in qas:\n", "        req = {\n", "            \"query\": qa[\"question\"],\n", "            \"workspace_ids\": [WORKSPACE_ID],\n", "            \"tool\": \"DocumentSearch\",\n", "            \"model\": \"alfred-4.1\"\n", "        }\n", "        async_requests.append(req)\n", "        # On stocke le nom cible sans extension pour la comparaison\n", "        target_image_names.append(img_file.replace(\".jpg\", \"\").replace(\".jpeg\", \"\").replace(\".png\", \"\"))\n", "\n", "# Fonction principale asynchrone\n", "async def run_async_benchmark():\n", "    async_api = AsyncDocumentSearch(api_key=API_KEY, base_url=BASE_URL)\n", "    results = []\n", "    all_questions = []\n", "    all_vlm_answers = []\n", "    all_expected_answers = []\n", "    found_image_names_list = []  # Pour stocker les documents trouvés par l'API\n", "\n", "    def progress_callback(done, total):\n", "        if done % 10 == 0 or done == total:\n", "            print(f\"{done}/{total} requêtes traitées...\")\n", "\n", "    start_time = time.time()\n", "    batch_results = await async_api.execute_batch(\n", "        requests_data=async_requests,\n", "        max_concurrent=10,\n", "        progress_callback=progress_callback\n", "    )\n", "    total_time = time.time() - start_time\n", "\n", "    for i, (req, res) in enumerate(zip(async_requests, batch_results)):\n", "        question = req[\"query\"]\n", "        answers = qa_data[i][\"answers\"] if i < len(qa_data) else []\n", "        api_response = res[\"result\"] if res[\"error\"] is None else {\"error\": res[\"error\"]}\n", "        vlm_answer = \"\"\n", "        found_image_names = []\n", "        if \"error\" not in api_response:\n", "            vlm_answer = api_response.get(\"answer\", \"\") or api_response.get(\"content\", \"\") or str(api_response)\n", "            # Extraction des documents trouvés\n", "            if \"documents\" in api_response:\n", "                for doc in api_response.get(\"documents\", []):\n", "                    name = doc.get(\"name\", \"\")\n", "                    found_image_names.append(name)\n", "        else:\n", "            vlm_answer = \"ERROR: Unable to process question\"\n", "        found_image_names_list.append(found_image_names)\n", "\n", "        results.append({\n", "            \"image_file\": None,  # Peut être retrouvé si besoin\n", "            \"question\": question,\n", "            \"answers\": answers,\n", "            \"api_response\": api_response,\n", "            \"vlm_answer\": vlm_answer,\n", "            \"found_image_names\": found_image_names\n", "        })\n", "        all_questions.append(question)\n", "        all_vlm_answers.append(vlm_answer)\n", "        all_expected_answers.append(answers if isinstance(answers, list) else [answers])\n", "\n", "    # Évaluation avec le juge DSPy\n", "    print(f\"\\nÉvaluation de {len(all_questions)} réponses avec juge DSPy...\")\n", "    try:\n", "        judgments = batch_judge(all_questions, all_vlm_answers, all_expected_answers)\n", "        for i, result in enumerate(results):\n", "            if \"error\" not in result[\"api_response\"]:\n", "                result[\"judge_verdict\"] = judgments[i] if i < len(judgments) else \"NO_JUDGMENT\"\n", "            else:\n", "                result[\"judge_verdict\"] = \"ERROR\"\n", "        correct_answers = judgments.count(\"YES\")\n", "        total_judged = len(judgments)\n", "        accuracy = (correct_answers / total_judged) * 100 if total_judged > 0 else 0\n", "        print(f\"Réponses correctes (DSPy Judge): {correct_answers}/{total_judged} ({accuracy:.2f}%)\")\n", "    except Exception as e:\n", "        print(f\"Erreur lors de l'évaluation par le juge DSPy: {str(e)}\")\n", "        accuracy = None\n", "\n", "    # Calcul du taux de bons documents trouvés\n", "    correct_image_found = 0\n", "    for i, found_names in enumerate(found_image_names_list):\n", "        target_name = target_image_names[i]\n", "        if target_name in found_names:\n", "            correct_image_found += 1\n", "    image_accuracy = (correct_image_found / len(found_image_names_list)) * 100 if len(found_image_names_list) > 0 else 0\n", "    print(f\"Bons documents trouvés: {correct_image_found}/{len(found_image_names_list)} ({image_accuracy:.2f}%)\")\n", "\n", "    # Sauvegarde des résultats\n", "    final_output = {\n", "        \"results\": results,\n", "        \"metadata\": {\n", "            \"total_process_time\": total_time,\n", "            \"total_requests\": len(results),\n", "            \"answer_accuracy\": accuracy,\n", "            \"image_search_accuracy\": image_accuracy\n", "        }\n", "    }\n", "    with open(\"images/results_batch_5_async.json\", \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(final_output, f, indent=2, ensure_ascii=False)\n", "    print(\"\\nRésultats sauvés dans images/results_batch_5_async.json\")\n", "\n", "# Await run\n", "await run_async_benchmark()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}