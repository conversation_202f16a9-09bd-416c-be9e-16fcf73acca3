<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGAS Report</title>
    <style>
        body {
            background: #18181B;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .main {
            font-family: "Noto Sans", ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            color: #FDFDFD;
            padding: 20px;
        }

        .header{
           display: flex;
           justify-content: center;
           align-items: center;
           border-bottom: 1px solid #6b7280;
           padding-bottom: 20px;
           margin-bottom: 40px;
         }
         .header > * {
           margin-inline: 20px;
         }
        #gsk-logo {
           padding-top: 10px;
         }

        h1 {
            font-size: 2.5rem;
            color: white;
        }

        .metrics-container {
            display: flex;
            flex-flow: row wrap;
            justify-content: center;
            gap: 32px;
            padding: 20px;
        }

        .metric-card {
            background-color: #14191B;
            border-radius: 16px;
            padding: 28px 32px 32px 32px;
            display: flex;
            flex-flow: column;
            align-items: center;
            flex-grow: 1;
            min-width: 200px;
            max-width: 250px;
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
        }

        .metric-title {
            font-size: 12px;
            font-weight: 500;
            color: #B1B1B1;
            padding-bottom: 8px;
            text-transform: uppercase;
            text-align: center;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 500;
            padding-bottom: 12px;
            text-align: center;
        }

         .text-green, .progress-green { color: #04B543; --color: #04B543; }
         .text-orange, .progress-orange { color: #E76E0F; --color: #E76E0F; }
         .text-red, .progress-red { color: #EA3829; --color: #EA3829; }

         .tooltip {
           position: relative;
           display: inline-block;
           cursor: help;
         }

         .tooltip .tooltiptext {
           visibility: hidden;
           width: 180px;
           background-color: #464646;
           color: #E6E6E6;
           text-align: center;
           border-radius: 6px;
           position: absolute;
           z-index: 1;
           bottom: 125%;
           left: 50%;
           margin-left: -90px;
           font-size: 12px;
           padding: 12px;
           opacity: 0;
           transition: opacity 0.3s;
         }

         .tooltip .tooltiptext::after {
           content: "";
           position: absolute;
           top: 100%;
           left: 50%;
           margin-left: -5px;
           border-width: 5px;
           border-style: solid;
           border-color: #464646 transparent transparent transparent;
         }

         .tooltip:hover .tooltiptext {
           visibility: visible;
           opacity: 1;
         }

         .value-faithfulness,
         .value-answer_relevancy,
         .value-context_precision,
         .value-context_recall {
            color: #fff !important;
         }

         .metric-bar { display: none; }

    </style>
</head>
<body>
    <div class="main">
        <div class="header">
             <svg xmlns="http://www.w3.org/2000/svg" width="60" height="30" viewBox="0 0 30 15" fill="none" id="gsk-logo">
                 <path fill="#fff" fill-rule="evenodd"
                     d="M22.504 1.549a4.196 4.196 0 0 1 2.573-.887v.002a3.783 3.783 0 0 1 2.706 1.086 3.783 3.783 0 0 1 1.126 2.69 3.771 3.771 0 0 1-1.126 2.69 3.77 3.77 0 0 1-2.706 1.085l-4.794.011-2.533 3.467L8.203 15l2.881-3.335a9.829 9.829 0 0 1-4.663-1.68H3.185L0 7.163h3.934C4.263 3.165 8.187 0 12.96 0c2.24 0 4.489.696 6.175 1.909a7.423 7.423 0 0 1 1.882 1.919 4.194 4.194 0 0 1 1.487-2.28ZM7.05 3.249l3.91 3.915h1.505L7.89 2.584a7.773 7.773 0 0 0-.84.665Zm4.079-2.008 5.923 5.923h1.503l-6.086-6.087c-.45.023-.898.078-1.34.164ZM4.574 8.226h-1.77l.784.693h1.584a8.454 8.454 0 0 1-.598-.693Zm9.479 0H5.984c1.469 1.477 3.656 2.377 5.977 2.422l2.092-2.422Zm-2.458 4.472 5.492-1.902 1.878-2.569h-3.508l-3.862 4.47Zm10.361-5.552h3.265a2.714 2.714 0 0 0 1.747-4.648 2.711 2.711 0 0 0-1.888-.773 3.127 3.127 0 0 0-3.123 3.124v2.297Zm3.659-3.73a.677.677 0 1 1-.134 1.348.677.677 0 0 1 .134-1.348Z"
                     clip-rule="evenodd" />
             </svg>
            <h1>RAG Evaluation Toolkit - RAGAS Metrics</h1>
        </div>

        <div class="metrics-container">

            <div class="metric-card">
                <div class="metric-title">Faithfulness</div>
                 <div class="metric-value tooltip value-faithfulness">
                    76.20%
                    <span class="tooltiptext">Measures the factual consistency of the generated answer against the given context. High score indicates the answer is grounded in the provided documents.</span>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">Answer Relevancy</div>
                <div class="metric-value tooltip value-answer_relevancy">
                    56.81%
                    <span class="tooltiptext">Evaluates how relevant the generated answer is to the original question. Low score might indicate the answer is incomplete or out of scope.</span>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">Context Precision</div>
                 <div class="metric-value tooltip value-context_precision">
                    67.29%
                    <span class="tooltiptext">Assesses the signal-to-noise ratio in the retrieved context. High score means most of the retrieved context was relevant for answering the question.</span>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-title">Context Recall</div>
                 <div class="metric-value tooltip value-context_recall">
                    74.49%
                    <span class="tooltiptext">Measures if all necessary information required to answer the question was present in the retrieved context.</span>
                 </div>
            </div>

        </div>
    </div>
</body>
</html> 