{"cells": [{"cell_type": "markdown", "id": "d165a280", "metadata": {}, "source": ["## Tester la sécurité générale d'un LLM"]}, {"cell_type": "markdown", "id": "d7a4b838", "metadata": {}, "source": ["### Préparer l'authentification globale"]}, {"cell_type": "code", "execution_count": null, "id": "1642a7e3", "metadata": {}, "outputs": [], "source": ["from azure.ai.inference import ChatCompletionsClient\n", "from azure.ai.inference.models import SystemMessage, UserMessage\n", "from azure.core.credentials import AzureKeyCredential\n", "from openai import AzureOpenAI\n", "import pandas as pd\n", "import giskard\n", "import os\n", "import dotenv\n", "\n", "# Load all env variables\n", "dotenv.load_dotenv()"]}, {"cell_type": "markdown", "id": "76477dba", "metadata": {}, "source": ["### <PERSON>er l'accès à Azure, nécessaire à Giskard"]}, {"cell_type": "code", "execution_count": null, "id": "abf2160a", "metadata": {}, "outputs": [], "source": ["env_azure_api_key = os.getenv(\"AZURE_API_KEY\")\n", "env_azure_base_endpoint = os.getenv(\"AZURE_API_BASE\")\n", "env_azure_version = os.getenv(\"AZURE_API_VERSION\")\n", "\n", "# Create the Azure OpenAI client\n", "azure_judge_client = AzureOpenAI(\n", "    api_key=env_azure_api_key,\n", "    api_version=env_azure_version,\n", "    azure_endpoint=env_azure_base_endpoint\n", ")\n", "\n", "# Test the connection\n", "try:\n", "    models = azure_judge_client.models.list()\n", "    print(\"Azure OpenAI connection successful\")\n", "except Exception as e:\n", "    print(f\"Azure OpenAI connection failed: {e}\")\n"]}, {"cell_type": "markdown", "id": "e00e9a48", "metadata": {}, "source": ["### Configurer le LLM utilisé par Giskard comme \"LLM-as-a-judge\""]}, {"cell_type": "code", "execution_count": 5, "id": "6b19408a", "metadata": {}, "outputs": [], "source": ["giskard.llm.set_llm_model(\"azure/gpt-4o-semarchy\")\n", "giskard.llm.set_embedding_model(\"azure/text-embedding-3-large\")"]}, {"cell_type": "markdown", "id": "1aa6c298", "metadata": {}, "source": ["### Configurer le déploiement Azure cible (<PERSON><PERSON><PERSON><PERSON>)"]}, {"cell_type": "code", "execution_count": null, "id": "088b3a99", "metadata": {}, "outputs": [], "source": ["env_target_azure_key = os.getenv(\"AZURE_TARGET_KEY\")\n", "env_target_azure_base = os.getenv(\"AZURE_TARGET_BASE\")\n", "\n", "# Create the Azure OpenAI client\n", "azure_target_client = ChatCompletionsClient(\n", "    endpoint=env_target_azure_base,\n", "    credential=AzureKeyCredential(env_target_azure_key)\n", ")\n", "\n", "# Test the connection\n", "try:\n", "    response = azure_target_client.complete(\n", "        messages=[\n", "            SystemMessage(content=\"You are a helpful assistant.\"),\n", "            UserMessage(content=\"Hello!\")\n", "        ],\n", "        max_tokens=10,\n", "        temperature=0.1,\n", "        top_p=0.1,\n", "        model=\"Mistral-Large-2411-ratp\"\n", "    )\n", "    print(\"Azure OpenAI connection successful\")\n", "except Exception as e:\n", "    print(f\"Azure OpenAI connection failed: {e}\")"]}, {"cell_type": "markdown", "id": "4ebdee31", "metadata": {}, "source": ["### Encapsuler l'appel au Azure cible pour permettre le traitement par Giskard"]}, {"cell_type": "code", "execution_count": null, "id": "48a2d3ab", "metadata": {}, "outputs": [], "source": ["def az_predict(df: pd.DataFrame):\n", "    \"\"\"Wraps the Azure Mistral LLM call for Giskard testing.\n", "    \n", "    Args:\n", "        df (pd.DataFrame): DataFrame containing questions to be answered (passed by <PERSON><PERSON><PERSON>)\n", "        \n", "    Returns:\n", "        list: List of model responses for each question\n", "    \"\"\"\n", "    def az_internal_call(question: str) -> str:\n", "        model_name = \"Mistral-Large-2411-ratp\"\n", "        messages = [\n", "            SystemMessage(content=\"You are a helpful assistant and you answer a wide set of questions. Please always be concise and polite.\"),\n", "            UserMessage(content=question)\n", "        ]\n", "        response = azure_target_client.complete(\n", "            messages=messages,\n", "            max_tokens=150,\n", "            temperature=0.1,\n", "            top_p=0.1,\n", "            model=model_name\n", "        )\n", "        \n", "        return response.choices[0].message.content\n", "    \n", "    return [az_internal_call(question) for question in df[\"question\"].values]\n", "\n", "# Create the Giskard model wrapper\n", "giskard_model = giskard.Model(\n", "    model=az_predict,\n", "    model_type=\"text_generation\",\n", "    name=\"Azure Mistral Large QA\",\n", "    description=\"This model uses Azure-hosted Mistral Large to answer general questions\",\n", "    feature_names=[\"question\"]\n", ")"]}, {"cell_type": "markdown", "id": "2fe94e53", "metadata": {}, "source": ["### Tester une complétion classique, sans <PERSON><PERSON><PERSON>, pour valider la fonction de prédiction"]}, {"cell_type": "code", "execution_count": null, "id": "2978f680", "metadata": {}, "outputs": [], "source": ["# Test the wrapped model\n", "test_question_set = pd.DataFrame({\n", "    \"question\": [\"What is the capital of France?\", \"What is 2+2?\"]\n", "})\n", "results = az_predict(test_question_set)\n", "print(results)"]}, {"cell_type": "markdown", "id": "f8e4e746", "metadata": {}, "source": ["### <PERSON><PERSON> and get the results"]}, {"cell_type": "code", "execution_count": null, "id": "a6881068", "metadata": {}, "outputs": [], "source": ["scan_results = giskard.scan(giskard_model)\n", "display(scan_results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}