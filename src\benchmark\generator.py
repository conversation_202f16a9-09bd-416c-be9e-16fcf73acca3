"""
Testset generator module. Wrapper of <PERSON><PERSON>ard to generate questions to ask to the RAG LLM.
It builds a standardized testset for <PERSON><PERSON><PERSON> using a knowledge base in .csv format containing one column of prechunked text.

Can chunk files of the knowledge base for you. Simply add a 'knowledge_files' directory at the root of the project. 
Ensure it only contains textual data to be extracted, no OCR is implemented.

"""

import os
from pathlib import Path
import glob
import logging
import pandas as pd
import giskard
from giskard.rag import generate_testset, KnowledgeBase
from langchain_community.document_loaders import (
    PyPDFLoader,
    TextLoader,
    UnstructuredMarkdownLoader,
    UnstructuredWordDocumentLoader,
)
from langchain.text_splitter import RecursiveCharacterTextSplitter
from typing import Optional, Union


class DataframeGenerator:
    """A class to generate the required dataframe (representing the knowledge base) for the Testset Generator.
    It can create that dataframe from a folder with files to be chunked or from an already provided single-column .csv"""

    def __init__(
        self,
        knowledge_path: Optional[Union[str, Path]] = None,
        csv_path: Optional[str] = None,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
    ):
        # Determine knowledge path: use provided or default to project-root 'knowledge_files'
        if knowledge_path:
            self.knowledge_path = Path(knowledge_path)
        else:
            self.knowledge_path = Path(__file__).parent.parent / "knowledge_files"
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"Knowledge path set to {self.knowledge_path.resolve()}")
        self.csv_path = csv_path
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            length_function=len,
        )

    def generate(self) -> pd.DataFrame:
        """Generate chunks DataFrame from directory or load from CSV."""
        if self._has_valid_directory():
            chunks = self._process_directory()
            if chunks:
                df = pd.DataFrame(chunks, columns=["content"])
                self._save_csv(df)
                return df

        # Fallback on a CSV file to be found at project root directory
        if not self.csv_path:
            src_dir = Path(__file__).parent.parent
            csv_files = list(src_dir.glob("*.csv"))
            if csv_files:
                self.csv_path = str(csv_files[0])
                self.logger.warning(f"No knowledge directory found, using CSV file : {self.csv_path}")

        if self.csv_path:
            return self._load_csv()

        msg = (
            "No valid source for text chunks. Provide a directory with text files "
            "or a single-column CSV file."
        )
        self.logger.error(msg)
        raise ValueError(msg)

    def _has_valid_directory(self) -> bool:
        return Path(self.knowledge_path).is_dir()

    def _get_files(self) -> list[str]:
        pdf = glob.glob(os.path.join(self.knowledge_path, "*.pdf"))
        txt = glob.glob(os.path.join(self.knowledge_path, "*.txt"))
        md = glob.glob(os.path.join(self.knowledge_path, "*.md"))
        doc = glob.glob(os.path.join(self.knowledge_path, "*.doc"))
        return pdf + txt + md + doc

    def _process_directory(self) -> list[str]:
        files = self._get_files()
        self.logger.info(
            f"Found {len(files)} processable files in {self.knowledge_path}"
        )
        chunks_list: list[str] = []
        for file in files:
            chunks_list.extend(self._process_file(file))
        return chunks_list

    def _process_file(self, file_path: str) -> list[str]:
        name = os.path.basename(file_path)
        self.logger.info(f"Processing file: {name}")
        try:
            ext = file_path.lower()
            if ext.endswith(".pdf"):
                loader = PyPDFLoader(file_path)
            elif ext.endswith(".txt"):
                loader = TextLoader(file_path, encoding="utf-8")
            elif ext.endswith(".md"):
                loader = UnstructuredMarkdownLoader(file_path, encoding="utf-8")
            elif ext.endswith(".doc"):
                loader = UnstructuredWordDocumentLoader(file_path)
            else:
                self.logger.warning(f"Unsupported file type for {name}")
                return []
            docs = loader.load()
            if not docs:
                self.logger.warning(f"{name} is empty or unreadable")
                return []
            splits = self.text_splitter.split_documents(docs)
            if not splits:
                self.logger.warning(f"No chunks created from {name}")
                return []
            self.logger.info(f"Created {len(splits)} chunks from {name}")
            return [chunk.page_content for chunk in splits]
        except Exception as e:
            self.logger.error(f"Error processing {name}: {e}")
            return []

    def _save_csv(self, df: pd.DataFrame) -> None:
        output = os.path.join(
            os.path.dirname(self.knowledge_path), "knowledge/knowledge_chunks.csv"
        )
        df.to_csv(output, index=False, encoding="utf-8")
        self.logger.info(f"Saved {len(df)} chunks to {output}")

    def _load_csv(self) -> pd.DataFrame:
        path = self.csv_path
        self.logger.info(f"Loading pre-chunked data from CSV: {path}")
        if not Path(path).exists() or not path.lower().endswith(".csv"):
            msg = f"Invalid CSV path: {path}"
            self.logger.error(msg)
            raise ValueError(msg)
        df = pd.read_csv(path, encoding="utf-8")
        if df.shape[1] != 1:
            msg = f"CSV must have exactly one column, found {df.shape[1]}"
            self.logger.error(msg)
            raise ValueError(msg)
        df.columns = ["content"]
        return df


class TestsetGenerator:
    """
    A configurable wrapper of the test question generator from Giskard.
    An agent description is critical and will help the judge LLM to understand the main topic of your docs.
    """

    def __init__(
        self,
        dataframe: pd.DataFrame,
        agent_description: str,
        llm_model: str,
        embedding_model: str,
        language: str = "fr",
        total_questions: int = 10,
    ):
        self.dataframe = dataframe
        self.language = language
        self.total_questions = total_questions
        self.agent_description = agent_description
        self.llm_model = llm_model
        self.embedding_model = embedding_model
        self.logger = logging.getLogger(self.__class__.__name__)

        # Configure judge LLM and embedding
        giskard.llm.set_llm_model(self.llm_model)
        giskard.llm.set_embedding_model(self.embedding_model)

    def generate(self, output_path: Path) -> pd.DataFrame:
        """
        Generate a test set using Giskard's testset generator.

        Args:
            output_path (Path): Path to save the generated test set JSON file

        Returns:
            pd.DataFrame: DataFrame containing the generated test set
        """
        # Initialize the KnowledgeBase
        self.logger.info(f"Initializing KnowledgeBase with {len(self.dataframe)} documents")
        kb = KnowledgeBase(self.dataframe)

        # Generate a test set
        self.logger.info(f"Generating test set with {self.total_questions} questions")
        testset = generate_testset(
            kb,
            num_questions=self.total_questions,
            language=self.language,
            agent_description=self.agent_description,
        )

        # Save the test set
        self.logger.info(f"Saving test set to {output_path}")
        testset.save(output_path)
