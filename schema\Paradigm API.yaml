openapi: 3.0.3
info:
  title: Paradigm API
  version: optimistic-orca
  description: A versatile and adaptable tool designed to integrate Generative AI
    into your applications
paths:
  /api/v2/chat/completions:
    post:
      operationId: api_v2_chat_completions_create
      description: |-
        This endpoint can be used to generate chat completions from a Large Language Model.

        It is a simple proxy forwarding your requests to the desired model.

        Any LightOn model is deployed on a vLLM-based image and is therefore compatible with the OpenAI request format.
      tags:
      - AI Models
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LLMV2'
            examples:
              LightOnModelExample:
                value:
                  model: alfred-40b-1123
                  messages:
                  - role: system
                    content: You are a helpful assistant.
                  - role: user
                    content: Hello!
                summary: LightOn model example
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LLMV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LLMV2'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /api/v2/chat/document-analysis:
    post:
      operationId: api_v2_chat_document_analysis_create
      description: |
        This public endpoint processes document analysis requests and initiates the analysis pipeline. Users can submit queries to analyze specific documents.

        Request Body:
        - `query` (required): The analysis request or question about the documents. This text will be used to analyze and generate responses about the document content.
        - `document_ids` (required): List of document IDs to analyze. These documents must be accessible to the user.
        - `model` (optional): Specific language model to use for analysis. If not provided, the system will use the default configured model.

        Response:
        - `chat_session_id`: Unique identifier to track and retrieve the analysis results.
      tags:
      - Files
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentAnalysisProcess'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentAnalysisProcess'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentAnalysisProcess'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentAnalysisProcessResponse'
          description: ''
  /api/v2/chat/document-analysis/{chat_response_id}:
    get:
      operationId: api_v2_chat_document_analysis_retrieve
      description: |-
        This public endpoint retrieves the status or results of a document analysis request.

        URL Path:
        - `chat_response_id` (required): The chat response id returned from the initial analysis request. Must be provided in the URL path.

        Response Types:
        - Complete analysis response when processing is finished
        - 404 error if no matching task is found

        Continue polling this endpoint until processing is complete to retrieve the final results.
      parameters:
      - in: path
        name: chat_response_id
        schema:
          type: integer
        required: true
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentAnalysisResponse'
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  detail:
                    type: string
                    example: Not found.
          description: ''
  /api/v2/chat/document-search:
    post:
      operationId: api_v2_chat_document_search_create
      description: |-
        This public endpoint handles chat interactions that involve responses generated from document analysis. It allows users to submit query, which the system processes to provide relevant responses by leveraging documents. You can customize the prompt of the model from the admin panel or complete the existing prompt with a dedicated user message.

        The `query` parameter should be a string.

        The `model` parameter is optional and specifies the LLM to use for generating responses. If not provided, a default model configured in the user's chat settings will be used.

        The `workspace_ids` parameter is optional and specifies the workspace IDs whose documentswill be added to the scope.

        The `file_ids` parameter is optional and specifies the document IDs that will be added to the scope.

        The `chat_session_id` parameter is optional and specifies the chat session ID for follow-up.

        The `company_scope` parameter is optional and specifies if the documents from the user's company will beadded to the scope.

        The `private_scope` parameter is optional and specifies if the documents from the user's private collection will be added to the scope.

        The `tool` parameter is optional and specifies the tool to use for the chat with doc session choose between 'VisionDocumentSearch' and 'DocumentSearch', the default is 'DocumentSearch'.
      tags:
      - Files
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentSearchInput'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentSearchInput'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentSearchInput'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentSearchResponse'
          description: ''
  /api/v2/chat/image-analysis:
    post:
      operationId: api_v2_chat_image_analysis_create
      description: |-
        This public endpoint handles chat interactions with images, enabling AI-powered visual analysis and conversation. Users can submit queries about specific documents containing images.

        Parameters:
        - `query` (required): The user's question or prompt about the image(s). This text will be used to analyze and generate responses about the visual content.
        - `document_ids` (required): List of document IDs containing images to analyze (min: 1, max: 5 documents). These documents must be accessible to the user.
        - `model` (optional): Specific vision-language model to use for analysis. If not provided, the system will use the default configured model.

        Response:
        - `answer`: The AI-generated response analyzing the images based on the user's query.

        The system will process the specified documents, analyze their visual content in relation to the query, and provide relevant insights and answers.
      tags:
      - Files
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatImageAnalysis'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatImageAnalysis'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatImageAnalysis'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatImageAnalysisResponse'
          description: ''
  /api/v2/companies/{id}:
    get:
      operationId: api_v2_companies_retrieve
      description: "This endpoint allows to retrieve a company. \n\n"
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - companies
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
          description: ''
  /api/v2/companies/{id}/update/:
    patch:
      operationId: api_v2_companies_update_partial_update
      description: "This endpoint allows to update a company . \n\nIt is restrict\
        \ to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - companies
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUpdateCompanyDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUpdateCompanyDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUpdateCompanyDeserializer'
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
          description: ''
  /api/v2/companies/{id}/users/:
    post:
      operationId: api_v2_companies_users_create
      description: "This endpoint allows to create a company member. \n\nIt is restrict\
        \ to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCompanyMemberDe'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CreateCompanyMemberDe'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateCompanyMemberDe'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyMember'
          description: ''
  /api/v2/companies/{id}/workspaces/:
    get:
      operationId: api_v2_companies_workspaces_list
      description: "This endpoint allows listing all workspaces of a company. \n\n\
        It is restrict to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - workspaces
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedWorkspaceList'
          description: ''
  /api/v2/companies/{id}/workspaces/create/:
    post:
      operationId: api_v2_companies_workspaces_create_create
      description: "This endpoint allows to create a workspace in a company. \n\n\
        It is restrict to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - workspaces
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWorkspaceDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CreateWorkspaceDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateWorkspaceDeserializer'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkspaceWithRelations'
          description: ''
  /api/v2/companies/create/:
    post:
      operationId: api_v2_companies_create_create
      description: "This endpoint allows to create a company.  \n\nOnly for admin\
        \ users. "
      tags:
      - companies
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCompanyDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CreateCompanyDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateCompanyDeserializer'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
          description: ''
  /api/v2/completions:
    post:
      operationId: api_v2_completions_create
      description: |-
        This endpoint can be used to generate completions from a Large Language Model.

        It is a simple proxy forwarding your requests to the desired model.

        Any LightOn model is deployed on a vLLM-based image and is therefore compatible with the OpenAI request format.
      tags:
      - AI Models
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LLMV2'
            examples:
              LightOnModelExample:
                value:
                  model: alfred-40b-1123
                  prompt: 'Hello, '
                summary: LightOn model example
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LLMV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LLMV2'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /api/v2/embeddings:
    post:
      operationId: api_v2_embeddings_create
      description: |-
        This endpoint can be used to convert text chunks into embeddings.

        It is a simple proxy forwarding your requests to the desired model.

        Any LightOn embedding model is compatible with the OpenAI request format.
      tags:
      - AI Models
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmbeddingV2'
            examples:
              LightOnModelExample:
                value:
                  model: multilingual-e5-large
                  input: Hello
                summary: LightOn model example
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmbeddingV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmbeddingV2'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /api/v2/feedback/feedback-types/:
    get:
      operationId: api_v2_feedback_feedback_types_list
      description: API endpoint for viewing and editing feedback types.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: unique_feedback
        schema:
          type: boolean
      - in: query
        name: value_type
        schema:
          type: string
          enum:
          - boolean
          - comment
          - float
          - tag
        description: |-
          * `boolean` - Boolean flag
          * `float` - Float range
          * `comment` - Free text comment
          * `tag` - String tag
      tags:
      - Feedbacks
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFeedbackTypeList'
          description: ''
  /api/v2/feedback/feedback-types/{id}/:
    get:
      operationId: api_v2_feedback_feedback_types_retrieve
      description: API endpoint for viewing and editing feedback types.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this feedback type.
        required: true
      tags:
      - Feedbacks
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeedbackType'
          description: ''
  /api/v2/files:
    get:
      operationId: api_v2_files_list
      description: This endpoint can be used to get the list of available documents
        for your user account, with optional filtering by workspace, private, or company
        scope.
      parameters:
      - in: query
        name: company_scope
        schema:
          type: boolean
        description: Include documents from the company collection if True.
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: private_scope
        schema:
          type: boolean
        description: Include documents from the user's private collection if True.
      - in: query
        name: workspace_scope
        schema:
          type: integer
        description: Include documents contained in workspace with ID given.
        explode: true
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedFileListResponseList'
          description: ''
    post:
      operationId: api_v2_files_create
      description: |-
        Endpoint to upload files.

        Files are saved to the persistent storage, parsed, chunked and embedded.

        The file is indexed as a document in the SQL database, with its chunks,

        and related to its owner, company and collection for permission application.

        To specify a workspace, set 'collection_type' to one of the following:
           - 'private' for a personal workspace
           - 'company' for a company workspace
           - 'workspace' for other workspaces

        If 'workspace' is specified, 'workspace_id' must be provided.
        If 'collection_type' is not specified, it defaults to 'company'.
      tags:
      - Files
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileCreate'
            examples:
              UploadAFile:
                value:
                  file: '@document.pdf'
                  collection_type: workspace
                  workspace_id: 1
                summary: Upload a file
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FileCreate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FileCreate'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileCreateResponse'
              examples:
                FileUploadedSuccessfully:
                  value:
                    id: 1
                    object: list
                    bytes: 20032
                    created_at: 1631533200
                    filename: document.pdf
                    purpose: documents
                    status: embedded
                  summary: File uploaded successfully
          description: ''
  /api/v2/files/{id}/ask-question:
    post:
      operationId: api_v2_files_ask_question_create
      description: |-
        This endpoint can be used to ask a question about a document.

        The question is required and should be a string .

        The id of the document is a path parameter.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - Files
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatWithDoc'
            examples:
              ExampleQuestionString:
                value:
                  question: example question
                summary: example question string
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChatWithDoc'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChatWithDoc'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionResponse'
          description: ''
  /api/v2/files/{id}:
    get:
      operationId: api_v2_files_retrieve
      description: Retrieve a single document by its ID. Only documents that the requesting
        user is authorized to access will be returned.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: The ID of the document to retrieve.
        required: true
      - in: query
        name: include_content
        schema:
          type: boolean
        description: Include the content of the document in the response.
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileRetrieveResponse'
          description: ''
    delete:
      operationId: api_v2_files_destroy
      description: |-
        Deletes a document and its associated embeddings.

        This endpoint deletes a document from the database and removes its associated embeddings.

        The document is identified by its unique ID.

        This operation requires authentication and permission to delete the document.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this Document.
        required: true
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '204':
          description: No response body
  /api/v2/files/{id}/chunks:
    get:
      operationId: api_v2_files_chunks_retrieve
      description: This endpoint returns all chunks for a given document.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this Document.
        required: true
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /api/v2/filter/chunks:
    post:
      operationId: api_v2_filter_chunks_create
      description: |+
        This endpoint can be used to filter document chunks based on a given query.

        The query should be a single string.

        Chunk ids should be a list of strings. Each string should be the UUID of an existing document chunk.

        The `model` field should be the name of the model to use for filtering.

        An optional integer `n` can be provided, which can be used to return the first `n` chunks that pass the filter.

      tags:
      - Files Search
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterChunks'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FilterChunks'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FilterChunks'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FilterResponse'
          description: ''
  /api/v2/models:
    get:
      operationId: api_v2_models_retrieve
      description: This endpoint can be used to get the list of available models for
        your API key.
      tags:
      - AI Models
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListMLModel'
          description: ''
  /api/v2/query:
    post:
      operationId: api_v2_query_create
      description: |-
        This endpoint can be used to retrieve top n chunks per query.

        The query can be a single string or a list of strings.

        The `collection` field can be used to specify the collection to query, which defaults to `base_collection`.

        An optional integer `n` can be provided in order to retrieve top n chunks, which defaults to 5 if not specified.
      tags:
      - Files Search
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Retrieve'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Retrieve'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Retrieve'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RetrieveResult'
          description: ''
  /api/v2/query/reformulation:
    post:
      operationId: api_v2_query_reformulation_create
      description: |+
        This endpoint can be used to reformulate a query in order to enhance retrieval.

        The query should be a single string or a list of string with up to 10 queries.

        The `model` field should be the name of the model to use for query formulation.

      tags:
      - Files Search
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QueryReformulation'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/QueryReformulation'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/QueryReformulation'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/QueryReformulationResult'
          description: ''
  /api/v2/rate/{feedback_type_id}/{completion_id}:
    post:
      operationId: api_v2_rate_create
      description: Log a feedback of a given type and linked to an existing completion.
      parameters:
      - in: path
        name: completion_id
        schema:
          type: string
        required: true
      - in: path
        name: feedback_type_id
        schema:
          type: integer
        required: true
      tags:
      - Feedbacks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Feedback'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Feedback'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Feedback'
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /api/v2/reporting/chat-sessions/:
    get:
      operationId: api_v2_reporting_chat_sessions_retrieve
      description: "This endpoint allows retrieving company chat sessions report.\
        \ \n\nThe start date is required, but the end date is by default today. \n\
        \nIt is restrict to admin and company admin users. \n\nAdmin users can optionally\
        \ filter by company"
      parameters:
      - in: query
        name: company_id
        schema:
          type: integer
      - in: query
        name: end_date
        schema:
          type: string
          format: date
      - in: query
        name: start_date
        schema:
          type: string
          format: date
        required: true
      tags:
      - reporting
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveChatSessionsReport'
          description: ''
  /api/v2/reporting/chats-feedback/:
    get:
      operationId: api_v2_reporting_chats_feedback_retrieve
      description: "This endpoint allows retrieving company chats feedback report.\
        \ \n\nThe start date is required, but the end date is by default today. \n\
        \nIt is restrict to admin and company admin users. \n\nAdmin users can optionally\
        \ filter by company"
      parameters:
      - in: query
        name: company_id
        schema:
          type: integer
      - in: query
        name: end_date
        schema:
          type: string
          format: date
      - in: query
        name: start_date
        schema:
          type: string
          format: date
        required: true
      tags:
      - reporting
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveChatsFeedbackReport'
          description: ''
  /api/v2/reporting/tools/:
    get:
      operationId: api_v2_reporting_tools_retrieve
      description: "This endpoint allows retrieving company tools and third party\
        \ tools report. \n\nThe start date is required, but the end date is by default\
        \ today. \n\nIt is restrict to admin and company admin users. \n\nAdmin users\
        \ can optionally filter by company"
      parameters:
      - in: query
        name: company_id
        schema:
          type: integer
      - in: query
        name: end_date
        schema:
          type: string
          format: date
      - in: query
        name: start_date
        schema:
          type: string
          format: date
        required: true
      tags:
      - reporting
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveToolsReport'
          description: ''
  /api/v2/reporting/used-documents/:
    get:
      operationId: api_v2_reporting_used_documents_retrieve
      description: "This endpoint allows retrieving company used documents report.\
        \ \n\nThe start date is required, but the end date is by default today. \n\
        \nIt is restrict to admin and company admin users. \n\nAdmin users can optionally\
        \ filter by company"
      parameters:
      - in: query
        name: company_id
        schema:
          type: integer
      - in: query
        name: end_date
        schema:
          type: string
          format: date
      - in: query
        name: start_date
        schema:
          type: string
          format: date
        required: true
      tags:
      - reporting
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveUsedDocumentsReport'
          description: ''
  /api/v2/reporting/users/:
    get:
      operationId: api_v2_reporting_users_retrieve
      description: "This endpoint allows retrieving company members report. \n\nThe\
        \ start date is required, but the end date is by default today. \n\nIt is\
        \ restrict to admin and company admin users. \n\nAdmin users can optionally\
        \ filter by company"
      parameters:
      - in: query
        name: company_id
        schema:
          type: integer
      - in: query
        name: end_date
        schema:
          type: string
          format: date
      - in: query
        name: start_date
        schema:
          type: string
          format: date
        required: true
      tags:
      - reporting
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrieveActiveUsersReport'
          description: ''
  /api/v2/tokenize:
    post:
      operationId: api_v2_tokenize_create
      description: |-
        This endpoint can be used to convert strings into tokens.

        It is a simple proxy forwarding your requests to the desired model.

        Any LightOn model is deployed on a vLLM-based image and is therefore compatible with the OpenAI request format.
      tags:
      - AI Models
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LLMV2'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LLMV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LLMV2'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /api/v2/tools/chatflows/:
    post:
      operationId: create_chatflow
      description: |-
        Create a new chatflow session with specified messages, model, and optional parameters.

        This endpoint starts a new chat session and returns the AI response based on the provided messages and configuration. You can optionally include tool configuration, workspace IDs, file IDs, and scope parameters to control the context of the conversation.
      tags:
      - Chatflow
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateChatflow'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CreateChatflow'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateChatflow'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateChatflowResponse'
          description: ''
        '400':
          description: No response body
        '401':
          description: No response body
        '403':
          description: No response body
        '429':
          description: No response body
  /api/v2/upload-session:
    post:
      operationId: api_v2_upload_session_create
      description: Create a new upload session
      tags:
      - Files
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUploadSession'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CreateUploadSession'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateUploadSession'
      security:
      - ApiKeyAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadSession'
          description: ''
  /api/v2/upload-session/{uuid}:
    get:
      operationId: api_v2_upload_session_retrieve
      description: Get the upload session details
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        description: The UUID of the upload session
        required: true
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadSession'
          description: ''
    post:
      operationId: api_v2_upload_session_create_2
      description: Upload a file to the upload session
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - Files
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/FileCreate'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: api_v2_upload_session_destroy
      description: Delete a session and all its documents
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
        required: true
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '204':
          description: No response body
  /api/v2/upload-session/deactivate:
    post:
      operationId: api_v2_upload_session_deactivate_create
      description: Deactivate last user sessions
      tags:
      - Files
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /api/v2/users/:
    get:
      operationId: api_v2_users_list
      description: "This endpoint allows listing all members of the requested user's\
        \ company. \n\nIt is restrict to admin and company admin users. \n\nAdmin\
        \ users can optionally filter members by company"
      parameters:
      - in: query
        name: company_id
        schema:
          type: integer
      - in: query
        name: is_active
        schema:
          type: boolean
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - users
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCompanyMemberList'
          description: ''
  /api/v2/users/{id}:
    get:
      operationId: api_v2_users_retrieve
      description: "This endpoint allows retrieving a company member details. \n\n\
        It is restrict to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyMember'
          description: ''
  /api/v2/users/{id}/groups/update/:
    put:
      operationId: api_v2_users_groups_update_update
      description: "This endpoint allows to update a company member groups. \n\nNote\
        \ that this will update all the groups of the member. \n\nIt is restrict to\
        \ admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyMemberGroupsDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UpdateCompanyMemberGroupsDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UpdateCompanyMemberGroupsDeserializer'
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyMember'
          description: ''
  /api/v2/users/{id}/update/:
    patch:
      operationId: api_v2_users_update_partial_update
      description: "This endpoint allows to update a company member. \n\nIt is restrict\
        \ to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUpdateCompanyMemberDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUpdateCompanyMemberDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUpdateCompanyMemberDeserializer'
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyMember'
          description: ''
  /api/v2/users/{id}/workspaces/update/:
    put:
      operationId: api_v2_users_workspaces_update_update
      description: "This endpoint allows to update a company member workspaces. \n\
        \nNote that this will update all the workspaces of the member. \n\nIt is restrict\
        \ to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCompanyMemberWorkspacesDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UpdateCompanyMemberWorkspacesDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UpdateCompanyMemberWorkspacesDeserializer'
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyMember'
          description: ''
  /api/v2/workspaces/{id}:
    get:
      operationId: api_v2_workspaces_retrieve
      description: "This endpoint allows to retrieve a workspace with his relations\
        \ (members and collections). \n\nIt is restrict to admin and company admin\
        \ users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - workspaces
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkspaceWithRelations'
          description: ''
  /api/v2/workspaces/{id}/members/update/:
    put:
      operationId: api_v2_workspaces_members_update_update
      description: "This endpoint allows to update workspace members. \n\nNote that\
        \ this will update all the members of the workspace. \n\nIt is restrict to\
        \ admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - workspaces
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWorkspaceMembersDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UpdateWorkspaceMembersDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UpdateWorkspaceMembersDeserializer'
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkspaceWithRelations'
          description: ''
  /api/v2/workspaces/{id}/update/:
    patch:
      operationId: api_v2_workspaces_update_partial_update
      description: "This endpoint allows to update a workspace in a company. \n\n\
        It is restrict to admin and company admin users."
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - workspaces
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUpdateWorkspaceDeserializer'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUpdateWorkspaceDeserializer'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUpdateWorkspaceDeserializer'
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkspaceWithRelations'
          description: ''
  /scim/v2/Groups:
    get:
      operationId: scim_v2_Groups_retrieve
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /scim/v2/ResourceTypes:
    get:
      operationId: scim_v2_ResourceTypes_retrieve
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /scim/v2/Schemas:
    get:
      operationId: scim_v2_Schemas_retrieve
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /scim/v2/ServiceProviderConfig:
    get:
      operationId: scim_v2_ServiceProviderConfig_retrieve
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
  /scim/v2/Users:
    get:
      operationId: scim_v2_Users_retrieve
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: scim_v2_Users_create
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
    put:
      operationId: scim_v2_Users_update
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
    patch:
      operationId: scim_v2_Users_partial_update
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          description: No response body
    delete:
      operationId: scim_v2_Users_destroy
      tags:
      - SCIM
      security:
      - ApiKeyAuth: []
      responses:
        '204':
          description: No response body
  /tools/chatflows/:
    post:
      operationId: create_chatflow_2
      description: |-
        Create a new chatflow session with specified messages, model, and optional parameters.

        This endpoint starts a new chat session and returns the AI response based on the provided messages and configuration. You can optionally include tool configuration, workspace IDs, file IDs, and scope parameters to control the context of the conversation.
      tags:
      - Chatflow
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateChatflow'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CreateChatflow'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateChatflow'
        required: true
      security:
      - ApiKeyAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateChatflowResponse'
          description: ''
        '400':
          description: No response body
        '401':
          description: No response body
        '403':
          description: No response body
        '429':
          description: No response body
components:
  schemas:
    Chat:
      type: object
      properties:
        user_queries_count:
          type: integer
        model_responses_with_docs_count:
          type: integer
        model_responses_count:
          type: integer
        last_model_response_date:
          type: string
          format: date-time
      required:
      - last_model_response_date
      - model_responses_count
      - model_responses_with_docs_count
      - user_queries_count
    ChatImageAnalysis:
      type: object
      properties:
        query:
          type: string
        document_ids:
          type: array
          items: {}
          maxItems: 5
          minItems: 1
        model:
          type: string
      required:
      - document_ids
      - query
    ChatImageAnalysisResponse:
      type: object
      properties:
        answer:
          type: string
      required:
      - answer
    ChatSessionsReportPerDate:
      type: object
      properties:
        date:
          type: string
          format: date
        sessions:
          $ref: '#/components/schemas/Session'
        chats:
          $ref: '#/components/schemas/Chat'
      required:
      - chats
      - date
      - sessions
    ChatSettingsLight:
      type: object
      properties:
        company:
          type: string
        instruction:
          type: string
      required:
      - company
      - instruction
    ChatWithDoc:
      type: object
      properties:
        question:
          type: string
          maxLength: 2048
      required:
      - question
    ChatsFeedback:
      type: object
      properties:
        likes_count:
          type: integer
        dislikes_count:
          type: integer
        copies_count:
          type: integer
      required:
      - copies_count
      - dislikes_count
      - likes_count
    ChatsFeedbackReportPerDate:
      type: object
      properties:
        date:
          type: string
          format: date
        model_responses_count:
          type: integer
        feedbacks:
          $ref: '#/components/schemas/ChatsFeedback'
      required:
      - date
      - feedbacks
      - model_responses_count
    Chunk:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        text:
          type: string
        metadata:
          type: object
          additionalProperties: {}
        filter_score:
          type: number
          format: double
      required:
      - filter_score
      - metadata
      - text
      - uuid
    Company:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 255
        owner:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        members:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - members
      - name
      - owner
    CompanyActiveUserReport:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/ShortCompany'
        total_historical_members:
          type: integer
        current_active_members:
          type: integer
      required:
      - company
      - current_active_members
      - total_historical_members
    CompanyActiveUserReportMetadata:
      type: object
      properties:
        total_historical_members:
          $ref: '#/components/schemas/Metadata'
        current_active_members:
          $ref: '#/components/schemas/Metadata'
      required:
      - current_active_members
      - total_historical_members
    CompanyChatSessionReport:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/ShortCompany'
        dates:
          type: array
          items:
            $ref: '#/components/schemas/ChatSessionsReportPerDate'
      required:
      - company
      - dates
    CompanyChatsFeedBackReport:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/ShortCompany'
        dates:
          type: array
          items:
            $ref: '#/components/schemas/ChatsFeedbackReportPerDate'
      required:
      - company
      - dates
    CompanyMember:
      type: object
      properties:
        id:
          type: integer
        username:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
        is_active:
          type: boolean
        date_joined:
          type: string
          format: date-time
        account_expiration_date:
          type: string
          format: date-time
        last_login:
          type: string
          format: date-time
        invitation_status:
          type: string
        language:
          type: string
        company:
          $ref: '#/components/schemas/ShortCompany'
        groups:
          type: array
          items:
            $ref: '#/components/schemas/TenantUserGroup'
        workspaces:
          type: array
          items:
            $ref: '#/components/schemas/Workspaces'
      required:
      - account_expiration_date
      - company
      - date_joined
      - email
      - first_name
      - groups
      - id
      - invitation_status
      - is_active
      - language
      - last_login
      - last_name
      - username
      - workspaces
    CompanyToolReport:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/ShortCompany'
        dates:
          type: array
          items:
            $ref: '#/components/schemas/ToolReportPerDate'
      required:
      - company
      - dates
    CompanyUsedDocumentsReport:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/ShortCompany'
        dates:
          type: array
          items:
            $ref: '#/components/schemas/UsedDocumentReportPerDate'
      required:
      - company
      - dates
    CreateChatflow:
      type: object
      description: Serializer for the Chat with Docs endpoint.
      properties:
        messages:
          type: string
        model:
          type: string
          nullable: true
          description: Model to use for the chatflow, must exist and be configured
            from the admin.
        tool:
          type: string
          nullable: true
          description: Tool to use for the chatflow, must exist and be configured
            from the admin.
        workspace_ids:
          type: array
          items:
            type: integer
          description: list of workspace IDs whose documents will be added to scope.
        file_ids:
          type: array
          items:
            type: integer
          description: list of document IDs that will be added to scope.
        company_scope:
          type: boolean
        private_scope:
          type: boolean
      required:
      - messages
    CreateChatflowResponse:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        chat_message:
          type: integer
          nullable: true
        text:
          type: string
          nullable: true
        error_message:
          type: string
          nullable: true
        completion:
          type: string
          format: uuid
          nullable: true
        is_chosen:
          type: boolean
        feedback:
          allOf:
          - $ref: '#/components/schemas/ResponseFeedback'
          readOnly: true
        document_extracts:
          type: array
          items:
            $ref: '#/components/schemas/DocumentExtract'
          readOnly: true
        source:
          $ref: '#/components/schemas/SourceEnum'
        flag_created_at:
          type: string
          nullable: true
          readOnly: true
        tool_call:
          allOf:
          - $ref: '#/components/schemas/ToolCall'
          readOnly: true
      required:
      - created_at
      - document_extracts
      - feedback
      - flag_created_at
      - id
      - tool_call
    CreateCompanyDeserializer:
      type: object
      properties:
        name:
          type: string
        dpo_email:
          type: string
          format: email
        max_users:
          type: integer
        allow_company_admins_to_manage_sso:
          type: boolean
          default: false
        storage_limit_company_ws:
          type: number
          format: double
          nullable: true
        storage_limit_personal_ws:
          type: number
          format: double
          nullable: true
        storage_limit_custom_ws:
          type: number
          format: double
          nullable: true
      required:
      - name
    CreateCompanyMemberDe:
      type: object
      properties:
        first_name:
          type: string
        last_name:
          type: string
        account_expiration_date:
          type: string
          format: date-time
        language:
          $ref: '#/components/schemas/LanguageEnum'
        username:
          type: string
          pattern: ^[\w.@+-]+$
        email:
          type: string
          format: email
        workspaces:
          type: array
          items:
            type: integer
        groups:
          type: array
          items:
            type: integer
      required:
      - email
      - username
    CreateUploadSession:
      type: object
      properties:
        ingestion_pipeline:
          $ref: '#/components/schemas/IngestionPipelineEnum'
    CreateWorkspaceDeserializer:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        members:
          type: array
          items:
            type: integer
      required:
      - description
      - name
    DeploymentTypeEnum:
      enum:
      - OpenAI
      - Self-Hosted
      type: string
      description: |-
        * `OpenAI` - Openai
        * `Self-Hosted` - Self Hosted
    DocumentAnalysisProcess:
      type: object
      properties:
        query:
          type: string
        document_ids:
          type: array
          items: {}
          maxItems: 5
          minItems: 1
        model:
          type: string
      required:
      - document_ids
      - query
    DocumentAnalysisProcessResponse:
      type: object
      properties:
        chat_response_id:
          type: integer
      required:
      - chat_response_id
    DocumentAnalysisResponse:
      type: object
      properties:
        status:
          type: string
        result:
          type: string
        detailed_analysis:
          type: string
        progress:
          type: string
          readOnly: true
      required:
      - progress
      - status
    DocumentExtract:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        chat_response_id:
          type: integer
          readOnly: true
        document_id:
          type: integer
          readOnly: true
        page_start:
          type: integer
          maximum: 2147483647
          minimum: 0
        page_end:
          type: integer
          maximum: 9223372036854775807
          minimum: 0
          format: int64
        text:
          type: string
          nullable: true
        document_name:
          type: string
          readOnly: true
        document_file_type:
          type: string
          readOnly: true
        metadata:
          type: object
          additionalProperties: {}
          readOnly: true
        document_external_url:
          type: string
          readOnly: true
        document_datasource_name:
          type: string
          readOnly: true
        document_workspace_name:
          type: string
          readOnly: true
      required:
      - chat_response_id
      - created_at
      - document_datasource_name
      - document_external_url
      - document_file_type
      - document_id
      - document_name
      - document_workspace_name
      - id
      - metadata
      - page_end
      - page_start
    DocumentSearchInput:
      type: object
      description: Serializer for the Chat with Docs endpoint.
      properties:
        query:
          type: string
        workspace_ids:
          type: array
          items:
            type: integer
          description: list of workspace IDs whose documents will be added to scope.
        file_ids:
          type: array
          items:
            type: integer
          description: list of document IDs that will be added to scope.
        chat_session_id:
          type: integer
        model:
          type: string
        company_scope:
          type: boolean
        private_scope:
          type: boolean
        tool:
          $ref: '#/components/schemas/ToolEnum'
      required:
      - query
    DocumentSearchResponse:
      type: object
      properties:
        chat_session_id:
          type: integer
        answer:
          type: string
        user_instructions:
          type: string
        chat_settings:
          $ref: '#/components/schemas/ChatSettingsLight'
        model:
          type: string
        documents:
          type: array
          items:
            $ref: '#/components/schemas/RagDocument'
      required:
      - answer
      - chat_session_id
      - chat_settings
      - documents
      - model
      - user_instructions
    EmbeddingV2:
      type: object
      properties:
        model:
          type: string
          description: Model to use, must exist and be configured from the admin.
      required:
      - model
    Feedback:
      type: object
      properties:
        value:
          type: number
          format: double
          nullable: true
        flag:
          type: boolean
          nullable: true
          description: Field used in case of boolean type
        comment:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
          maxLength: 120
        display_value:
          oneOf:
          - type: boolean
          - type: string
          - type: number
            format: double
          - type: integer
          readOnly: true
      required:
      - display_value
    FeedbackType:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          nullable: true
          maxLength: 250
        value_type:
          $ref: '#/components/schemas/ValueTypeEnum'
        value_range_start:
          type: number
          format: double
          nullable: true
        value_range_end:
          type: number
          format: double
          nullable: true
        unique_feedback:
          type: boolean
          description: Defines if related feedback can have only one instance per
            completion or many
      required:
      - id
      - value_type
    FileCreate:
      type: object
      description: Mixin to validate file extension and size
      properties:
        title:
          type: string
          nullable: true
          maxLength: 255
        filename:
          type: string
          nullable: true
          maxLength: 512
        file:
          type: string
          format: uri
        collection:
          type: integer
          nullable: true
        owner:
          type: integer
          writeOnly: true
        company:
          type: integer
          writeOnly: true
        content:
          type: string
          nullable: true
        status:
          $ref: '#/components/schemas/Status88dEnum'
        file_type:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/FileTypeEnum'
          - $ref: '#/components/schemas/NullEnum'
        collection_type:
          allOf:
          - $ref: '#/components/schemas/CollectionTypeEnum'
          writeOnly: true
        workspace_id:
          type: integer
          writeOnly: true
        chunk_size:
          type: integer
        chunk_overlap:
          type: number
          format: double
        OCR_AGENT:
          type: string
        OCR_COMPLETE_DOC_TABLE_EXTRACTION:
          type: boolean
          nullable: true
        OCR_HI_RES_MODEL_NAME:
          type: string
        OCR_STRATEGY:
          type: string
        OCR_TIMEOUT:
          type: integer
        OCR_URL:
          type: string
        IS_VISUAL_INGESTION_ON:
          type: boolean
          nullable: true
        DEFAULT_PIPELINE:
          type: string
      required:
      - file
    FileCreateResponse:
      type: object
      properties:
        id:
          type: integer
        object:
          type: string
          readOnly: true
        bytes:
          type: integer
          readOnly: true
        created_at:
          type: integer
          readOnly: true
        filename:
          type: string
          readOnly: true
        purpose:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
      required:
      - bytes
      - created_at
      - filename
      - id
      - object
      - purpose
      - status
    FileListResponse:
      type: object
      properties:
        id:
          type: integer
        object:
          type: string
          readOnly: true
        created_at:
          type: integer
          readOnly: true
        filename:
          type: string
          readOnly: true
        purpose:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
      required:
      - created_at
      - filename
      - id
      - object
      - purpose
      - status
    FileRetrieveResponse:
      type: object
      properties:
        id:
          type: integer
        object:
          type: string
          readOnly: true
        created_at:
          type: integer
          readOnly: true
        filename:
          type: string
          readOnly: true
        purpose:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
        content:
          type: string
          nullable: true
      required:
      - created_at
      - filename
      - id
      - object
      - purpose
      - status
    FilterChunks:
      type: object
      description: Serializer dedicated to the input request of the filtering endpoint.
      properties:
        query:
          type: string
        chunk_ids:
          type: array
          items:
            type: string
        n:
          type: integer
          nullable: true
        model:
          type: string
          description: |-
            Model to use for the filtering, must exist and be configured from the admin.
            If no model is given, will try to use the "reranker" finetune.
      required:
      - chunk_ids
      - model
      - query
    FilterResponse:
      type: object
      properties:
        query:
          type: string
        chunks:
          type: array
          items:
            $ref: '#/components/schemas/Chunk'
      required:
      - chunks
      - query
    IngestionPipelineEnum:
      enum:
      - v1.0
      - v2.1
      - v2.1.1
      - v2.2
      - v2.2.1
      type: string
      description: |-
        * `v1.0` - v1.0 : Fast & streamlined text parser
        * `v2.1` - v2.1 : Enhanced table parser
        * `v2.1.1` - v2.1.1 : Layout analysis/OCR and new chunker
        * `v2.2` - v2.2 : Visual parser
        * `v2.2.1` - v2.2.1 : Visual parser with new chunker
    LLMV2:
      type: object
      properties:
        model:
          type: string
          description: Model to use, must exist and be configured from the admin.
      required:
      - model
    LanguageEnum:
      enum:
      - en
      - fr
      - de
      type: string
      description: |-
        * `en` - English
        * `fr` - French
        * `de` - German
    ListDocumentChunk:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        text:
          type: string
        title:
          type: string
          readOnly: true
      required:
      - id
      - text
      - title
    ListMLModel:
      type: object
      properties:
        object:
          type: string
          readOnly: true
          default: list
        data:
          type: array
          items:
            $ref: '#/components/schemas/MLModel'
          readOnly: true
      required:
      - data
      - object
    MLModel:
      type: object
      properties:
        object:
          type: string
          readOnly: true
          default: model
        name:
          type: string
          maxLength: 120
        model_type:
          $ref: '#/components/schemas/ModelTypeEnum'
        deployment_type:
          $ref: '#/components/schemas/DeploymentTypeEnum'
        enabled:
          type: boolean
        technical_name:
          type: string
          maxLength: 120
        start_messages_template:
          type: string
          description: 'List of dictionary of messages to be used to start the conversation.
            Authorized labels in the template: [''date'', ''company_instructions'',
            ''user_first_name'', ''user_last_name'', ''user_instructions'']'
          maxLength: 5000
        instructions:
          type: string
          description: Instructions for the model to use, as a string template. Leave
            blank and save for default instructions.
          maxLength: 5000
      required:
      - name
      - object
    Metadata:
      type: object
      properties:
        description:
          type: string
        unit:
          type: string
      required:
      - description
      - unit
    ModelTypeEnum:
      enum:
      - Large Language Model
      - Embedding Model
      - Vision Language Model
      type: string
      description: |-
        * `Large Language Model` - Large Language Model
        * `Embedding Model` - Embedding Model
        * `Vision Language Model` - Vision Language Model
    PaginatedCompanyMemberList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/CompanyMember'
    PaginatedFeedbackTypeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/FeedbackType'
    PaginatedFileListResponseList:
      type: object
      properties:
        object:
          type: string
          example: list
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: https://paradigm.lighton.ai/api/v2/files/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: https://paradigm.lighton.ai/api/v2/files/?page=2
        data:
          type: array
          items:
            $ref: '#/components/schemas/FileListResponse'
    PaginatedWorkspaceList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Workspace'
    PatchedUpdateCompanyDeserializer:
      type: object
      properties:
        name:
          type: string
        dpo_email:
          type: string
          format: email
        max_users:
          type: integer
        allow_company_admins_to_manage_sso:
          type: boolean
          default: false
        storage_limit_company_ws:
          type: number
          format: double
          nullable: true
        storage_limit_personal_ws:
          type: number
          format: double
          nullable: true
        storage_limit_custom_ws:
          type: number
          format: double
          nullable: true
    PatchedUpdateCompanyMemberDeserializer:
      type: object
      properties:
        first_name:
          type: string
        last_name:
          type: string
        account_expiration_date:
          type: string
          format: date-time
        language:
          $ref: '#/components/schemas/LanguageEnum'
    PatchedUpdateWorkspaceDeserializer:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
    QueryReformulation:
      type: object
      description: Serializer for reformulating a query.
      properties:
        query:
          type: string
        model:
          type: string
          description: |-
            Model to use for the reformulation, must exist and be configured from the admin.
                If no model is given, will try to use the "rewriter" finetune.
      required:
      - model
      - query
    QueryReformulationResult:
      type: object
      description: Serializer for the result of a the query reformulation function.
      properties:
        query:
          type: string
        reformulation:
          type: string
      required:
      - query
      - reformulation
    QuestionResponse:
      type: object
      description: Serializer for the result of a query.
      properties:
        response:
          type: string
        chunks:
          type: array
          items:
            $ref: '#/components/schemas/ListDocumentChunk'
      required:
      - chunks
      - response
    RagDocument:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        document_extracts:
          type: array
          items:
            $ref: '#/components/schemas/DocumentExtract'
      required:
      - document_extracts
      - id
      - name
    Retrieve:
      type: object
      description: Serializer for input queries to the retrieve endpoint.
      properties:
        query:
          type: string
        collection:
          type: string
          nullable: true
        n:
          type: integer
          nullable: true
      required:
      - query
    RetrieveActiveUsersReport:
      type: object
      properties:
        reporting:
          type: array
          items:
            $ref: '#/components/schemas/CompanyActiveUserReport'
        metadata:
          $ref: '#/components/schemas/CompanyActiveUserReportMetadata'
      required:
      - metadata
      - reporting
    RetrieveChatSessionsReport:
      type: object
      properties:
        metadata:
          $ref: '#/components/schemas/RetrieveChatSessionsReportMetadata'
        reporting:
          type: array
          items:
            $ref: '#/components/schemas/CompanyChatSessionReport'
      required:
      - metadata
      - reporting
    RetrieveChatSessionsReportMetadata:
      type: object
      properties:
        sessions_count:
          $ref: '#/components/schemas/Metadata'
        model_responses_with_docs_count:
          $ref: '#/components/schemas/Metadata'
        unique_models_count:
          $ref: '#/components/schemas/Metadata'
        average_session_length:
          $ref: '#/components/schemas/Metadata'
        user_queries_count:
          $ref: '#/components/schemas/Metadata'
        model_responses_count:
          $ref: '#/components/schemas/Metadata'
        last_model_response_date:
          $ref: '#/components/schemas/Metadata'
      required:
      - average_session_length
      - last_model_response_date
      - model_responses_count
      - model_responses_with_docs_count
      - sessions_count
      - unique_models_count
      - user_queries_count
    RetrieveChatsFeedbackReport:
      type: object
      properties:
        metadata:
          $ref: '#/components/schemas/RetrieveChatsFeedbackReportMetadata'
        reporting:
          type: array
          items:
            $ref: '#/components/schemas/CompanyChatsFeedBackReport'
      required:
      - metadata
      - reporting
    RetrieveChatsFeedbackReportMetadata:
      type: object
      properties:
        likes_count:
          $ref: '#/components/schemas/Metadata'
        dislikes_count:
          $ref: '#/components/schemas/Metadata'
        copies_count:
          $ref: '#/components/schemas/Metadata'
        model_responses_count:
          $ref: '#/components/schemas/Metadata'
      required:
      - copies_count
      - dislikes_count
      - likes_count
      - model_responses_count
    RetrieveResult:
      type: object
      description: Serializer for the result of a query.
      properties:
        query:
          type: string
        chunks:
          type: array
          items:
            $ref: '#/components/schemas/RetrieveResultChunk'
      required:
      - chunks
    RetrieveResultChunk:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        text:
          type: string
        metadata:
          type: object
          additionalProperties: {}
        score:
          type: number
          format: double
      required:
      - metadata
      - score
      - text
      - uuid
    RetrieveToolsReport:
      type: object
      properties:
        reporting:
          type: array
          items:
            $ref: '#/components/schemas/CompanyToolReport'
      required:
      - reporting
    RetrieveUsedDocumentsReport:
      type: object
      properties:
        metadata:
          $ref: '#/components/schemas/RetrieveUsedDocumentsReportMetadata'
        reporting:
          type: array
          items:
            $ref: '#/components/schemas/CompanyUsedDocumentsReport'
      required:
      - metadata
      - reporting
    RetrieveUsedDocumentsReportMetadata:
      type: object
      properties:
        usage_count:
          $ref: '#/components/schemas/Metadata'
      required:
      - usage_count
    Session:
      type: object
      properties:
        sessions_count:
          type: integer
        unique_models_count:
          type: integer
        average_session_length:
          type: number
          format: double
      required:
      - average_session_length
      - sessions_count
      - unique_models_count
    ShortCompany:
      type: object
      properties:
        name:
          type: string
        id:
          type: integer
      required:
      - id
      - name
    SourceEnum:
      enum:
      - embeddings
      - corpus
      type: string
      description: |-
        * `embeddings` - embeddings
        * `corpus` - corpus
    Status88dEnum:
      enum:
      - pending
      - parsing
      - parsing_failed
      - embedding
      - embedding_failed
      - embedded
      - fail
      - updating
      type: string
      description: |-
        * `pending` - Pending
        * `parsing` - Parsing
        * `parsing_failed` - Parsing Failed
        * `embedding` - Embedding
        * `embedding_failed` - Embedding Failed
        * `embedded` - Embedded
        * `fail` - Fail
        * `updating` - Updating
    TenantUserGroup:
      type: object
      properties:
        name:
          type: string
        id:
          type: integer
      required:
      - id
      - name
    ToolEnum:
      enum:
      - VisionDocumentSearch
      - DocumentSearch
      type: string
      description: |-
        * `VisionDocumentSearch` - VisionDocumentSearch
        * `DocumentSearch` - DocumentSearch
    ToolReport:
      type: object
      properties:
        name:
          type: string
        count:
          type: integer
      required:
      - count
      - name
    ToolReportPerDate:
      type: object
      properties:
        date:
          type: string
          format: date
        tools:
          type: array
          items:
            $ref: '#/components/schemas/ToolReport'
        third_party_tools:
          type: array
          items:
            $ref: '#/components/schemas/ToolReport'
      required:
      - date
      - third_party_tools
      - tools
    UpdateCompanyMemberGroupsDeserializer:
      type: object
      properties:
        groups:
          type: array
          items:
            type: integer
    UpdateCompanyMemberWorkspacesDeserializer:
      type: object
      properties:
        workspaces:
          type: array
          items:
            type: integer
    UpdateWorkspaceMembersDeserializer:
      type: object
      properties:
        members:
          type: array
          items:
            type: integer
    UploadSession:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        is_valid:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        documents:
          type: array
          items:
            $ref: '#/components/schemas/UploadSessionDocument'
      required:
      - created_at
      - documents
      - is_valid
      - updated_at
      - uuid
    UploadSessionDocument:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        title:
          type: string
          nullable: true
          maxLength: 255
        collection:
          type: integer
          nullable: true
        status:
          $ref: '#/components/schemas/Status88dEnum'
        updated_at:
          type: string
          format: date-time
          readOnly: true
        workspace:
          type: string
          readOnly: true
      required:
      - id
      - updated_at
      - workspace
    UsedDocument:
      type: object
      properties:
        document_filename:
          type: string
        document_title:
          type: string
        document_id:
          type: integer
        usage_count:
          type: integer
      required:
      - document_filename
      - document_id
      - document_title
      - usage_count
    UsedDocumentReportPerDate:
      type: object
      properties:
        date:
          type: string
          format: date
        documents:
          type: array
          items:
            $ref: '#/components/schemas/UsedDocument'
      required:
      - date
      - documents
    User:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
          title: Adresse e-mail
        first_name:
          type: string
          title: Prénom
          maxLength: 150
        last_name:
          type: string
          title: Nom
          maxLength: 150
        is_staff:
          type: boolean
          readOnly: true
          title: Statut équipe
          description: Précise si l’utilisateur peut se connecter à ce site d'administration.
        companies:
          type: array
          items:
            type: string
          readOnly: true
        workspaces:
          type: array
          items:
            type: string
          readOnly: true
        user_prompt:
          type: string
          nullable: true
          readOnly: true
      required:
      - companies
      - email
      - id
      - is_staff
      - user_prompt
      - workspaces
    ValueTypeEnum:
      enum:
      - boolean
      - float
      - comment
      - tag
      type: string
      description: |-
        * `boolean` - Boolean flag
        * `float` - Float range
        * `comment` - Free text comment
        * `tag` - String tag
    Workspace:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required:
      - created_at
      - description
      - id
      - name
      - updated_at
    WorkspaceCollection:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        collection_type:
          type: string
      required:
      - collection_type
      - id
      - name
    WorkspaceMember:
      type: object
      properties:
        id:
          type: integer
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
      required:
      - email
      - first_name
      - id
      - last_name
    WorkspaceWithRelations:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        collections:
          type: array
          items:
            $ref: '#/components/schemas/WorkspaceCollection'
        members:
          type: array
          items:
            $ref: '#/components/schemas/WorkspaceMember'
      required:
      - collections
      - created_at
      - description
      - id
      - members
      - name
      - updated_at
    Workspaces:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
      required:
      - description
      - id
      - name
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
      description: 'Token-based authentication with required prefix ''Bearer ''. For
        example: `Bearer <api_key>`'
servers:
- url: https://paradigm.lighton.ai
